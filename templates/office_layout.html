{% extends "base.html" %}

{% block title %}办公区域布局 - {{ office.name }} - 座位表管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>{{ office.name }} - 办公区域布局</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">{{ office.floor.building.city.name }}</li>
                <li class="breadcrumb-item">{{ office.floor.building.name }}</li>
                <li class="breadcrumb-item">{{ office.floor.name }}</li>
                <li class="breadcrumb-item active">{{ office.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">返回控制台</a>
        {% if current_user.is_admin %}
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createLayoutModal">创建布局</button>
        {% endif %}
    </div>
</div>

<!-- 工具栏 -->
{% if current_user.is_admin %}
<div class="card mb-4">
    <div class="card-body">
        <h5>布局工具</h5>
        <div class="btn-group" role="group">
            <input type="radio" class="btn-check" name="drawMode" id="seatMode" value="seat" checked>
            <label class="btn btn-outline-primary" for="seatMode">
                <i class="fas fa-chair"></i> 座位
            </label>
            
            <input type="radio" class="btn-check" name="drawMode" id="meetingRoomMode" value="meeting_room">
            <label class="btn btn-outline-success" for="meetingRoomMode">
                <i class="fas fa-users"></i> 会议室
            </label>
            
            <input type="radio" class="btn-check" name="drawMode" id="obstacleMode" value="obstacle">
            <label class="btn btn-outline-warning" for="obstacleMode">
                <i class="fas fa-square"></i> 障碍物
            </label>
            
            <input type="radio" class="btn-check" name="drawMode" id="eraseMode" value="erase">
            <label class="btn btn-outline-danger" for="eraseMode">
                <i class="fas fa-eraser"></i> 擦除
            </label>
        </div>
        
        <div class="ms-3 d-inline-block">
            <button class="btn btn-info" onclick="toggleGrid()">
                <i class="fas fa-th"></i> 显示网格
            </button>
            <button class="btn btn-secondary" onclick="clearLayout()">
                <i class="fas fa-trash"></i> 清空布局
            </button>
            <button class="btn btn-success" onclick="saveLayout()">
                <i class="fas fa-save"></i> 保存布局
            </button>
        </div>
    </div>
</div>
{% endif %}

<!-- 布局显示区域 -->
{% if seat_grids %}
    {% for grid in seat_grids %}
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ grid.name }} ({{ grid.rows }}行 × {{ grid.cols }}列)</h5>
            {% if current_user.is_admin %}
            <div>
                <button class="btn btn-sm btn-info" onclick="editLayout({{ grid.id }})">编辑布局</button>
                <button class="btn btn-sm btn-warning" onclick="editGrid({{ grid.id }})">编辑设置</button>
                <button class="btn btn-sm btn-danger" onclick="deleteGrid({{ grid.id }})">删除</button>
            </div>
            {% endif %}
        </div>
        <div class="card-body">
            <!-- 图例 -->
            <div class="legend mb-3">
                <span class="legend-item">
                    <span class="legend-color seat-available"></span> 空闲座位
                </span>
                <span class="legend-item">
                    <span class="legend-color seat-occupied"></span> 已占用座位
                </span>
                <span class="legend-item">
                    <span class="legend-color meeting-room"></span> 会议室
                </span>
                <span class="legend-item">
                    <span class="legend-color obstacle"></span> 障碍物
                </span>
            </div>
            
            <!-- 布局网格 -->
            <div class="office-layout" id="layout-{{ grid.id }}" 
                 data-grid-id="{{ grid.id }}" 
                 data-rows="{{ grid.rows }}" 
                 data-cols="{{ grid.cols }}">
                <!-- 布局将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="alert alert-info">
        <h4>暂无办公布局</h4>
        <p>该办公区域还没有创建布局。</p>
        {% if current_user.is_admin %}
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createLayoutModal">创建第一个布局</button>
        {% endif %}
    </div>
{% endif %}

<!-- 创建布局模态框 -->
{% if current_user.is_admin %}
<div class="modal fade" id="createLayoutModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建办公布局</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createLayoutForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="layoutName" class="form-label">布局名称</label>
                        <input type="text" class="form-control" id="layoutName" name="name" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="layoutRows" class="form-label">行数</label>
                            <input type="number" class="form-control" id="layoutRows" name="rows" min="5" max="30" value="10" required>
                        </div>
                        <div class="col-md-6">
                            <label for="layoutCols" class="form-label">列数</label>
                            <input type="number" class="form-control" id="layoutCols" name="cols" min="5" max="30" value="15" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">建议：6×10 适合小型办公区域，10×15 适合中型办公区域</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 会议室设置模态框 -->
<div class="modal fade" id="meetingRoomModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">会议室设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="meetingRoomForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="roomName" class="form-label">会议室名称</label>
                        <input type="text" class="form-control" id="roomName" name="name" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="roomCapacity" class="form-label">容纳人数</label>
                            <input type="number" class="form-control" id="roomCapacity" name="capacity" min="2" max="50" value="8" required>
                        </div>
                        <div class="col-md-6">
                            <label for="roomType" class="form-label">房间类型</label>
                            <select class="form-select" id="roomType" name="room_type">
                                <option value="meeting">会议室</option>
                                <option value="conference">大会议室</option>
                                <option value="phone_booth">电话亭</option>
                                <option value="training">培训室</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="roomEquipment" class="form-label">设备信息</label>
                        <textarea class="form-control" id="roomEquipment" name="equipment" rows="3" placeholder="如：投影仪、白板、电话会议设备等"></textarea>
                    </div>
                    <div id="roomCoordinates" class="alert alert-info">
                        <strong>选中区域：</strong><span id="coordinatesText">请在布局中选择区域</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建会议室</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<!-- 座位分配模态框 -->
<div class="modal fade" id="seatAssignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">座位分配</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="seatAssignForm">
                <div class="modal-body">
                    <div id="seatInfo" class="mb-3"></div>
                    <div class="mb-3">
                        <label for="employeeSelect" class="form-label">选择员工</label>
                        <select class="form-select" id="employeeSelect" name="user_id">
                            <option value="">请选择员工</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="assignNotes" class="form-label">备注</label>
                        <textarea class="form-control" id="assignNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">分配座位</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/office-layout.js') }}"></script>
<script>
const officeId = {{ office.id }};
let currentSeatId = null;
let currentGridId = null;
let drawMode = 'seat';
let isDrawing = false;
let selectedArea = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeOfficeLayouts();
    setupEventListeners();
});

function initializeOfficeLayouts() {
    const layouts = document.querySelectorAll('.office-layout');
    layouts.forEach(layout => {
        const gridId = layout.dataset.gridId;
        const rows = parseInt(layout.dataset.rows);
        const cols = parseInt(layout.dataset.cols);
        generateOfficeLayout(layout, gridId, rows, cols);
    });
}

function setupEventListeners() {
    // 绘制模式切换
    document.querySelectorAll('input[name="drawMode"]').forEach(radio => {
        radio.addEventListener('change', function() {
            drawMode = this.value;
            updateCursor();
        });
    });
    
    // 创建布局表单
    const createLayoutForm = document.getElementById('createLayoutForm');
    if (createLayoutForm) {
        createLayoutForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const name = formData.get('name');
            const rows = parseInt(formData.get('rows'));
            const cols = parseInt(formData.get('cols'));
            
            createSeatGrid(officeId, name, rows, cols)
                .then(result => {
                    if (result.success) {
                        showAlert(result.message, 'success');
                        bootstrap.Modal.getInstance(document.getElementById('createLayoutModal')).hide();
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showAlert(result.message || '创建失败', 'danger');
                    }
                });
        });
    }
}

function updateCursor() {
    const layouts = document.querySelectorAll('.office-layout');
    layouts.forEach(layout => {
        layout.className = layout.className.replace(/cursor-\w+/g, '');
        layout.classList.add(`cursor-${drawMode}`);
    });
}
</script>
{% endblock %}
