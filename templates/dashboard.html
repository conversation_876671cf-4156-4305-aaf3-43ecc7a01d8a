{% extends "base.html" %}

{% block title %}控制台 - 座位表管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <h2>座位表查看</h2>
        <div class="card">
            <div class="card-body">
                <form id="locationForm">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="citySelect" class="form-label">选择城市</label>
                            <select class="form-select" id="citySelect" name="city_id">
                                <option value="">请选择城市</option>
                                {% for city in cities %}
                                <option value="{{ city.id }}">{{ city.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="buildingSelect" class="form-label">选择建筑</label>
                            <select class="form-select" id="buildingSelect" name="building_id" disabled>
                                <option value="">请先选择城市</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="floorSelect" class="form-label">选择楼层</label>
                            <select class="form-select" id="floorSelect" name="floor_id" disabled>
                                <option value="">请先选择建筑</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="officeSelect" class="form-label">选择办公区域</label>
                            <select class="form-select" id="officeSelect" name="office_id" disabled>
                                <option value="">请先选择楼层</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="button" class="btn btn-primary" id="viewSeatingBtn" disabled>查看座位表</button>
                        <button type="button" class="btn btn-success" id="viewLayoutBtn" disabled>办公布局</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <h2>员工查询</h2>
        <div class="card">
            <div class="card-body">
                <form id="searchForm">
                    <div class="mb-3">
                        <label for="searchEmployeeId" class="form-label">员工工号</label>
                        <input type="text" class="form-control" id="searchEmployeeId" placeholder="输入员工工号">
                    </div>
                    <button type="submit" class="btn btn-success">查询位置</button>
                </form>
                
                <div id="searchResult" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <h6>查询结果：</h6>
                        <div id="employeeInfo"></div>
                        <button type="button" class="btn btn-sm btn-primary mt-2" id="goToSeatBtn">查看座位</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <h2>快速操作</h2>
        <div class="card">
            <div class="card-body">
                <div class="row">
                    {% if current_user.is_admin %}
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="card-title">管理后台</h5>
                                <p class="card-text">管理城市、建筑、楼层和座位表</p>
                                <a href="{{ url_for('admin') }}" class="btn btn-primary">进入管理</a>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="card-title">我的座位</h5>
                                <p class="card-text">查看我当前的座位分配</p>
                                <button class="btn btn-success" id="mySeatsBtn">查看我的座位</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 级联选择逻辑
document.getElementById('citySelect').addEventListener('change', function() {
    const cityId = this.value;
    const buildingSelect = document.getElementById('buildingSelect');
    const floorSelect = document.getElementById('floorSelect');
    const officeSelect = document.getElementById('officeSelect');
    
    // 重置后续选择
    buildingSelect.innerHTML = '<option value="">请选择建筑</option>';
    floorSelect.innerHTML = '<option value="">请先选择建筑</option>';
    officeSelect.innerHTML = '<option value="">请先选择楼层</option>';
    
    buildingSelect.disabled = !cityId;
    floorSelect.disabled = true;
    officeSelect.disabled = true;
    document.getElementById('viewSeatingBtn').disabled = true;
    
    if (cityId) {
        fetch(`/api/buildings/${cityId}`)
            .then(response => response.json())
            .then(buildings => {
                buildings.forEach(building => {
                    const option = document.createElement('option');
                    option.value = building.id;
                    option.textContent = building.name;
                    buildingSelect.appendChild(option);
                });
            });
    }
});

document.getElementById('buildingSelect').addEventListener('change', function() {
    const buildingId = this.value;
    const floorSelect = document.getElementById('floorSelect');
    const officeSelect = document.getElementById('officeSelect');
    
    floorSelect.innerHTML = '<option value="">请选择楼层</option>';
    officeSelect.innerHTML = '<option value="">请先选择楼层</option>';
    
    floorSelect.disabled = !buildingId;
    officeSelect.disabled = true;
    document.getElementById('viewSeatingBtn').disabled = true;
    
    if (buildingId) {
        fetch(`/api/floors/${buildingId}`)
            .then(response => response.json())
            .then(floors => {
                floors.forEach(floor => {
                    const option = document.createElement('option');
                    option.value = floor.id;
                    option.textContent = floor.name;
                    floorSelect.appendChild(option);
                });
            });
    }
});

document.getElementById('floorSelect').addEventListener('change', function() {
    const floorId = this.value;
    const officeSelect = document.getElementById('officeSelect');
    
    officeSelect.innerHTML = '<option value="">请选择办公区域</option>';
    officeSelect.disabled = !floorId;
    document.getElementById('viewSeatingBtn').disabled = true;
    
    if (floorId) {
        fetch(`/api/offices/${floorId}`)
            .then(response => response.json())
            .then(offices => {
                offices.forEach(office => {
                    const option = document.createElement('option');
                    option.value = office.id;
                    option.textContent = office.name;
                    officeSelect.appendChild(option);
                });
            });
    }
});

document.getElementById('officeSelect').addEventListener('change', function() {
    const officeId = this.value;
    document.getElementById('viewSeatingBtn').disabled = !officeId;
    document.getElementById('viewLayoutBtn').disabled = !officeId;
});

document.getElementById('viewSeatingBtn').addEventListener('click', function() {
    const officeId = document.getElementById('officeSelect').value;
    if (officeId) {
        window.location.href = `/seating/${officeId}`;
    }
});

document.getElementById('viewLayoutBtn').addEventListener('click', function() {
    const officeId = document.getElementById('officeSelect').value;
    if (officeId) {
        window.location.href = `/office-layout/${officeId}`;
    }
});
</script>
{% endblock %}
