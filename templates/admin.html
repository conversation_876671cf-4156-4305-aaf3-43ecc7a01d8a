{% extends "base.html" %}

{% block title %}管理后台 - 座位表管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>管理后台</h2>
    <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">返回控制台</a>
</div>

<!-- 导航标签 -->
<ul class="nav nav-tabs" id="adminTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="locations-tab" data-bs-toggle="tab" data-bs-target="#locations" type="button" role="tab">位置管理</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab">用户管理</button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="seats-tab" data-bs-toggle="tab" data-bs-target="#seats" type="button" role="tab">座位管理</button>
    </li>
</ul>

<div class="tab-content" id="adminTabsContent">
    <!-- 位置管理标签页 -->
    <div class="tab-pane fade show active" id="locations" role="tabpanel">
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5>城市管理</h5>
                        <button class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addCityModal">添加城市</button>
                    </div>
                    <div class="card-body">
                        <div class="list-group" id="cityList">
                            {% for city in cities %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ city.name }}</strong>
                                    <small class="text-muted">({{ city.code }})</small>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editCity({{ city.id }})">编辑</button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteCity({{ city.id }})">删除</button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>层级结构预览</h5>
                    </div>
                    <div class="card-body">
                        <div id="locationTree">
                            {% for city in cities %}
                            <div class="mb-3">
                                <strong>{{ city.name }}</strong>
                                {% for building in city.buildings %}
                                <div class="ms-3">
                                    <i class="fas fa-building"></i> {{ building.name }}
                                    {% for floor in building.floors %}
                                    <div class="ms-4">
                                        <i class="fas fa-layer-group"></i> {{ floor.name }}
                                        {% for office in floor.offices %}
                                        <div class="ms-5">
                                            <i class="fas fa-door-open"></i> {{ office.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endfor %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 用户管理标签页 -->
    <div class="tab-pane fade" id="users" role="tabpanel">
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5>用户列表</h5>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">添加用户</button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>工号</th>
                                <th>姓名</th>
                                <th>邮箱</th>
                                <th>部门</th>
                                <th>职位</th>
                                <th>角色</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.employee_id }}</td>
                                <td>{{ user.name }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.department or '-' }}</td>
                                <td>{{ user.position or '-' }}</td>
                                <td>
                                    {% if user.is_admin %}
                                    <span class="badge bg-danger">管理员</span>
                                    {% else %}
                                    <span class="badge bg-primary">普通用户</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser({{ user.id }})">编辑</button>
                                    {% if not user.is_admin or users|length > 1 %}
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }})">删除</button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 座位管理标签页 -->
    <div class="tab-pane fade" id="seats" role="tabpanel">
        <div class="card mt-4">
            <div class="card-header">
                <h5>座位分配统计</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h3 class="text-success">0</h3>
                                <p class="card-text">总座位数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h3 class="text-primary">0</h3>
                                <p class="card-text">已分配</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-warning">
                            <div class="card-body text-center">
                                <h3 class="text-warning">0</h3>
                                <p class="card-text">空闲座位</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h3 class="text-info">0%</h3>
                                <p class="card-text">使用率</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h6>快速操作</h6>
                    <button class="btn btn-outline-primary me-2">批量导入员工</button>
                    <button class="btn btn-outline-success me-2">导出座位表</button>
                    <button class="btn btn-outline-info">生成报告</button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加城市模态框 -->
<div class="modal fade" id="addCityModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加城市</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCityForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="cityName" class="form-label">城市名称</label>
                        <input type="text" class="form-control" id="cityName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="cityCode" class="form-label">城市代码</label>
                        <input type="text" class="form-control" id="cityCode" name="code" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 添加用户模态框 -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userEmployeeId" class="form-label">工号</label>
                                <input type="text" class="form-control" id="userEmployeeId" name="employee_id" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userName" class="form-label">姓名</label>
                                <input type="text" class="form-control" id="userName" name="name" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="userEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="userEmail" name="email" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userDepartment" class="form-label">部门</label>
                                <input type="text" class="form-control" id="userDepartment" name="department">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userPosition" class="form-label">职位</label>
                                <input type="text" class="form-control" id="userPosition" name="position">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="userPassword" class="form-label">初始密码</label>
                        <input type="password" class="form-control" id="userPassword" name="password" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="userIsAdmin" name="is_admin">
                        <label class="form-check-label" for="userIsAdmin">管理员权限</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">添加</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 管理员功能JavaScript
function editCity(cityId) {
    // TODO: 实现编辑城市功能
    alert('编辑城市功能开发中...');
}

function deleteCity(cityId) {
    if (confirm('确定要删除这个城市吗？这将删除该城市下的所有建筑、楼层和办公区域。')) {
        // TODO: 实现删除城市功能
        alert('删除城市功能开发中...');
    }
}

function editUser(userId) {
    // TODO: 实现编辑用户功能
    alert('编辑用户功能开发中...');
}

function deleteUser(userId) {
    if (confirm('确定要删除这个用户吗？')) {
        // TODO: 实现删除用户功能
        alert('删除用户功能开发中...');
    }
}

// 表单提交处理
document.getElementById('addCityForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // TODO: 实现添加城市API调用
    alert('添加城市功能开发中...');
});

document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // TODO: 实现添加用户API调用
    alert('添加用户功能开发中...');
});
</script>
{% endblock %}
