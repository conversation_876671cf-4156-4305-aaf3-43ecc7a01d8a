{% extends "base.html" %}

{% block title %}座位表 - {{ office.name }} - 座位表管理系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2>{{ office.name }} - 座位表</h2>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">{{ office.floor.building.city.name }}</li>
                <li class="breadcrumb-item">{{ office.floor.building.name }}</li>
                <li class="breadcrumb-item">{{ office.floor.name }}</li>
                <li class="breadcrumb-item active">{{ office.name }}</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">返回控制台</a>
        {% if current_user.is_admin %}
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createGridModal">创建座位表</button>
        {% endif %}
    </div>
</div>

{% if seat_grids %}
    {% for grid in seat_grids %}
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ grid.name }} ({{ grid.rows }}行 × {{ grid.cols }}列)</h5>
            {% if current_user.is_admin %}
            <div>
                <button class="btn btn-sm btn-warning" onclick="editGrid({{ grid.id }})">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteGrid({{ grid.id }})">删除</button>
            </div>
            {% endif %}
        </div>
        <div class="card-body">
            <div class="seat-grid" id="grid-{{ grid.id }}" data-grid-id="{{ grid.id }}" data-rows="{{ grid.rows }}" data-cols="{{ grid.cols }}">
                <!-- 座位表格将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
    {% endfor %}
{% else %}
    <div class="alert alert-info">
        <h4>暂无座位表</h4>
        <p>该办公区域还没有创建座位表。</p>
        {% if current_user.is_admin %}
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createGridModal">创建第一个座位表</button>
        {% endif %}
    </div>
{% endif %}

<!-- 创建座位表模态框 -->
{% if current_user.is_admin %}
<div class="modal fade" id="createGridModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建座位表</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createGridForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="gridName" class="form-label">座位表名称</label>
                        <input type="text" class="form-control" id="gridName" name="name" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <label for="gridRows" class="form-label">行数</label>
                            <input type="number" class="form-control" id="gridRows" name="rows" min="1" max="20" value="5" required>
                        </div>
                        <div class="col-md-6">
                            <label for="gridCols" class="form-label">列数</label>
                            <input type="number" class="form-control" id="gridCols" name="cols" min="1" max="20" value="8" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">创建</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<!-- 座位分配模态框 -->
<div class="modal fade" id="seatAssignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">座位分配</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="seatAssignForm">
                <div class="modal-body">
                    <div id="seatInfo" class="mb-3"></div>
                    <div class="mb-3">
                        <label for="employeeSelect" class="form-label">选择员工</label>
                        <select class="form-select" id="employeeSelect" name="user_id">
                            <option value="">请选择员工</option>
                            <!-- 员工选项将通过JavaScript动态加载 -->
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="assignNotes" class="form-label">备注</label>
                        <textarea class="form-control" id="assignNotes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">分配座位</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
const officeId = {{ office.id }};
let currentSeatId = null;

// 页面加载完成后初始化座位表
document.addEventListener('DOMContentLoaded', function() {
    initializeSeatingCharts();
});

function initializeSeatingCharts() {
    const grids = document.querySelectorAll('.seat-grid');
    grids.forEach(grid => {
        const gridId = grid.dataset.gridId;
        const rows = parseInt(grid.dataset.rows);
        const cols = parseInt(grid.dataset.cols);
        generateSeatGrid(grid, gridId, rows, cols);
    });
}

function generateSeatGrid(container, gridId, rows, cols) {
    container.innerHTML = '';
    
    const table = document.createElement('table');
    table.className = 'seat-table';
    
    for (let row = 1; row <= rows; row++) {
        const tr = document.createElement('tr');
        
        for (let col = 1; col <= cols; col++) {
            const td = document.createElement('td');
            const seatCode = `${String.fromCharCode(64 + row)}${col.toString().padStart(2, '0')}`;
            
            td.className = 'seat-cell available';
            td.dataset.row = row;
            td.dataset.col = col;
            td.dataset.seatCode = seatCode;
            td.innerHTML = `
                <div class="seat-content">
                    <div class="seat-code">${seatCode}</div>
                    <div class="seat-employee"></div>
                </div>
            `;
            
            td.addEventListener('click', function() {
                handleSeatClick(this, gridId);
            });
            
            tr.appendChild(td);
        }
        
        table.appendChild(tr);
    }
    
    container.appendChild(table);
    
    // 加载座位分配信息
    loadSeatAssignments(gridId);
}

function handleSeatClick(seatCell, gridId) {
    if (!{{ 'true' if current_user.is_admin else 'false' }}) {
        // 普通用户只能查看座位信息
        showSeatInfo(seatCell);
        return;
    }

    // 管理员可以分配座位
    const row = seatCell.dataset.row;
    const col = seatCell.dataset.col;
    const seatCode = seatCell.dataset.seatCode;
    const seatId = seatCell.dataset.seatId;

    // 设置当前座位ID
    window.currentSeatId = seatId;

    document.getElementById('seatInfo').innerHTML = `
        <strong>座位：</strong>${seatCode} (第${row}排第${col}个位置)
    `;

    // 加载员工列表
    loadEmployeeList();

    // 显示分配模态框
    new bootstrap.Modal(document.getElementById('seatAssignModal')).show();
}

function showSeatInfo(seatCell) {
    const seatCode = seatCell.dataset.seatCode;
    const employeeDiv = seatCell.querySelector('.seat-employee');
    const employeeName = employeeDiv.textContent;
    
    if (employeeName) {
        alert(`座位 ${seatCode}\n员工：${employeeName}`);
    } else {
        alert(`座位 ${seatCode}\n状态：空闲`);
    }
}

// 这些函数现在在seating.js中定义
</script>
{% endblock %}
