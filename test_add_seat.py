#!/usr/bin/env python3
"""
测试添加座位API的脚本
"""

import requests
import json

def test_add_seat_api():
    # 测试数据
    base_url = "http://localhost:5000"
    
    # 首先登录获取session
    login_data = {
        'employee_id': 'admin',
        'password': 'admin123'
    }
    
    session = requests.Session()
    
    # 登录
    print("1. 测试登录...")
    login_response = session.post(f"{base_url}/login", data=login_data)
    if login_response.status_code == 200:
        print("✓ 登录成功")
    else:
        print(f"✗ 登录失败: {login_response.status_code}")
        return
    
    # 测试添加座位API
    print("\n2. 测试添加座位API...")
    seat_data = {
        'seat_grid_id': 1,  # 假设存在ID为1的座位表
        'row_number': 1,
        'col_number': 1,
        'seat_code': 'TEST01'
    }
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    response = session.post(
        f"{base_url}/api/seat", 
        data=json.dumps(seat_data),
        headers=headers
    )
    
    print(f"响应状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 添加座位成功")
            print(f"座位ID: {result.get('seat_id')}")
        else:
            print(f"✗ 添加座位失败: {result.get('error', result.get('message'))}")
    else:
        print(f"✗ API调用失败: {response.status_code}")

if __name__ == '__main__':
    test_add_seat_api()
