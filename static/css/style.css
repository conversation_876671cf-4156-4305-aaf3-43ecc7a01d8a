/* 座位表样式 */
.seat-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 5px;
    margin: 20px 0;
}

.seat-cell {
    width: 80px;
    height: 60px;
    border: 2px solid #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    vertical-align: middle;
    position: relative;
    background: #f8f9fa;
}

.seat-cell:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.seat-cell.available {
    border-color: #28a745;
    background: #d4edda;
}

.seat-cell.occupied {
    border-color: #dc3545;
    background: #f8d7da;
}

.seat-cell.highlighted {
    border-color: #ffc107;
    background: #fff3cd;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.seat-content {
    padding: 2px;
    font-size: 10px;
    line-height: 1.2;
}

.seat-code {
    font-weight: bold;
    color: #495057;
    margin-bottom: 2px;
}

.seat-employee {
    color: #007bff;
    font-size: 9px;
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 座位表网格容器 */
.seat-grid {
    overflow-x: auto;
    padding: 10px;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 面包屑导航样式 */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item {
    color: #6c757d;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 500;
}

/* 卡片样式增强 */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
    border-radius: 8px;
}

.card-header {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0 !important;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
}

/* 表单样式 */
.form-select, .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
}

.form-select:focus, .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* 模态框样式 */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 8px 8px;
}

/* 警告框样式 */
.alert {
    border-radius: 8px;
    border: none;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .seat-cell {
        width: 60px;
        height: 45px;
        font-size: 8px;
    }
    
    .seat-content {
        padding: 1px;
    }
    
    .seat-code {
        font-size: 8px;
    }
    
    .seat-employee {
        font-size: 7px;
    }
    
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip-inner {
    background-color: #333;
    color: white;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-available {
    background-color: #28a745;
}

.status-occupied {
    background-color: #dc3545;
}

.status-highlighted {
    background-color: #ffc107;
}

/* 办公布局样式 */
.office-layout {
    width: 100%;
    overflow: auto;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.layout-grid {
    min-width: 800px;
    min-height: 400px;
    user-select: none;
}

.layout-grid.show-grid {
    background-image:
        linear-gradient(to right, #e0e0e0 1px, transparent 1px),
        linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
    background-size: 20px 20px;
}

.layout-cell {
    width: 40px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    text-align: center;
}

.layout-cell:hover {
    transform: scale(1.05);
    z-index: 10;
}

/* 不同类型的单元格样式 */
.layout-cell.empty {
    background: #f8f9fa;
    border-color: #dee2e6;
}

.layout-cell.seat-available {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.layout-cell.seat-occupied {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.layout-cell.meeting-room {
    background: #cce5ff;
    border-color: #007bff;
    color: #004085;
    font-weight: bold;
}

.layout-cell.obstacle {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.layout-cell.selecting {
    background: #e7f3ff;
    border-color: #0066cc;
    border-width: 2px;
    animation: pulse-select 0.5s infinite;
}

@keyframes pulse-select {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
}

/* 单元格内容 */
.cell-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 2px;
}

.cell-label {
    font-size: 8px;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 1px;
}

.cell-info {
    font-size: 7px;
    line-height: 1;
    opacity: 0.8;
}

/* 绘制模式光标 */
.cursor-seat {
    cursor: crosshair;
}

.cursor-meeting_room {
    cursor: cell;
}

.cursor-obstacle {
    cursor: not-allowed;
}

.cursor-erase {
    cursor: grab;
}

/* 工具栏样式 */
.btn-check:checked + .btn {
    background-color: var(--bs-primary);
    border-color: var(--bs-primary);
    color: white;
}

/* 图例样式 */
.legend {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #495057;
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    margin-right: 6px;
    border: 1px solid #dee2e6;
}

.legend-color.seat-available {
    background: #d4edda;
    border-color: #28a745;
}

.legend-color.seat-occupied {
    background: #f8d7da;
    border-color: #dc3545;
}

.legend-color.meeting-room {
    background: #cce5ff;
    border-color: #007bff;
}

.legend-color.obstacle {
    background: #fff3cd;
    border-color: #ffc107;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .layout-cell {
        width: 30px;
        height: 30px;
        font-size: 8px;
    }

    .cell-label {
        font-size: 6px;
    }

    .cell-info {
        font-size: 5px;
    }

    .legend {
        gap: 10px;
    }

    .legend-item {
        font-size: 10px;
    }

    .legend-color {
        width: 12px;
        height: 12px;
    }
}
