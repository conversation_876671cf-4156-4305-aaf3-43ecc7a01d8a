// 办公布局管理JavaScript

// 全局变量
let currentLayout = null;
let selectedCells = [];
let isSelecting = false;
let startCell = null;

// 生成办公布局网格
function generateOfficeLayout(container, gridId, rows, cols) {
    container.innerHTML = '';
    container.className = 'office-layout';
    
    // 创建网格容器
    const grid = document.createElement('div');
    grid.className = 'layout-grid';
    grid.style.display = 'grid';
    grid.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
    grid.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
    grid.style.gap = '2px';
    grid.style.padding = '10px';
    grid.style.backgroundColor = '#f8f9fa';
    grid.style.border = '2px solid #dee2e6';
    grid.style.borderRadius = '8px';
    
    // 创建网格单元格
    for (let row = 1; row <= rows; row++) {
        for (let col = 1; col <= cols; col++) {
            const cell = document.createElement('div');
            cell.className = 'layout-cell empty';
            cell.dataset.row = row;
            cell.dataset.col = col;
            cell.dataset.gridId = gridId;
            
            // 添加单元格内容
            cell.innerHTML = `
                <div class="cell-content">
                    <div class="cell-label"></div>
                    <div class="cell-info"></div>
                </div>
            `;
            
            // 添加事件监听器
            setupCellEventListeners(cell);
            
            grid.appendChild(cell);
        }
    }
    
    container.appendChild(grid);
    
    // 加载现有的布局数据
    loadLayoutData(gridId);
}

// 设置单元格事件监听器
function setupCellEventListeners(cell) {
    // 鼠标按下开始选择
    cell.addEventListener('mousedown', function(e) {
        e.preventDefault();
        // 检查用户权限（从全局变量或页面获取）
        const isAdmin = document.body.dataset.userIsAdmin === 'true';
        if (!isAdmin) {
            handleCellClick(this);
            return;
        }

        if (window.drawMode === 'meeting_room') {
            startAreaSelection(this);
        } else {
            handleCellDraw(this);
        }
    });
    
    // 鼠标进入继续选择
    cell.addEventListener('mouseenter', function(e) {
        if (isSelecting && window.drawMode === 'meeting_room') {
            updateAreaSelection(this);
        } else if (isDrawing && window.drawMode !== 'meeting_room') {
            handleCellDraw(this);
        }
    });
    
    // 鼠标抬起结束选择
    cell.addEventListener('mouseup', function(e) {
        if (isSelecting && window.drawMode === 'meeting_room') {
            endAreaSelection();
        }
    });
    
    // 右键菜单
    cell.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        const isAdmin = document.body.dataset.userIsAdmin === 'true';
        if (isAdmin) {
            showCellContextMenu(this, e.clientX, e.clientY);
        }
    });
}

// 处理单元格点击（普通用户）
function handleCellClick(cell) {
    const cellType = cell.classList.contains('seat-occupied') || cell.classList.contains('seat-available') ? 'seat' : 
                    cell.classList.contains('meeting-room') ? 'meeting_room' : 'empty';
    
    if (cellType === 'seat') {
        showSeatInfo(cell);
    } else if (cellType === 'meeting_room') {
        showMeetingRoomInfo(cell);
    }
}

// 处理单元格绘制（管理员）
function handleCellDraw(cell) {
    const mode = window.drawMode;
    
    switch (mode) {
        case 'seat':
            if (!cell.classList.contains('meeting-room')) {
                cell.className = 'layout-cell seat-available';
                cell.querySelector('.cell-label').textContent = generateSeatCode(cell);
                cell.querySelector('.cell-info').textContent = '';
            }
            break;
            
        case 'obstacle':
            cell.className = 'layout-cell obstacle';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '障碍物';
            break;
            
        case 'erase':
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
            break;
    }
}

// 开始区域选择（会议室）
function startAreaSelection(cell) {
    isSelecting = true;
    startCell = cell;
    selectedCells = [cell];
    cell.classList.add('selecting');
}

// 更新区域选择
function updateAreaSelection(cell) {
    if (!isSelecting || !startCell) return;
    
    // 清除之前的选择
    selectedCells.forEach(c => c.classList.remove('selecting'));
    selectedCells = [];
    
    // 计算选择区域
    const startRow = parseInt(startCell.dataset.row);
    const startCol = parseInt(startCell.dataset.col);
    const endRow = parseInt(cell.dataset.row);
    const endCol = parseInt(cell.dataset.col);
    
    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);
    
    // 选择矩形区域内的所有单元格
    const gridId = cell.dataset.gridId;
    for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
            const targetCell = document.querySelector(`[data-grid-id="${gridId}"][data-row="${row}"][data-col="${col}"]`);
            if (targetCell && !targetCell.classList.contains('meeting-room')) {
                targetCell.classList.add('selecting');
                selectedCells.push(targetCell);
            }
        }
    }
}

// 结束区域选择
function endAreaSelection() {
    isSelecting = false;
    
    if (selectedCells.length > 1) {
        // 显示会议室设置模态框
        showMeetingRoomModal();
    } else {
        // 清除选择
        selectedCells.forEach(cell => cell.classList.remove('selecting'));
        selectedCells = [];
    }
    
    startCell = null;
}

// 显示会议室设置模态框
function showMeetingRoomModal() {
    if (selectedCells.length === 0) return;
    
    const firstCell = selectedCells[0];
    const lastCell = selectedCells[selectedCells.length - 1];
    
    const startRow = Math.min(...selectedCells.map(c => parseInt(c.dataset.row)));
    const endRow = Math.max(...selectedCells.map(c => parseInt(c.dataset.row)));
    const startCol = Math.min(...selectedCells.map(c => parseInt(c.dataset.col)));
    const endCol = Math.max(...selectedCells.map(c => parseInt(c.dataset.col)));
    
    // 更新坐标显示
    document.getElementById('coordinatesText').textContent = 
        `行 ${startRow}-${endRow}, 列 ${startCol}-${endCol} (${selectedCells.length} 个单元格)`;
    
    // 存储选择区域信息
    window.selectedArea = {
        start_row: startRow,
        start_col: startCol,
        end_row: endRow,
        end_col: endCol,
        grid_id: firstCell.dataset.gridId
    };
    
    // 显示模态框
    new bootstrap.Modal(document.getElementById('meetingRoomModal')).show();
}

// 生成座位编号
function generateSeatCode(cell) {
    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);
    return `${String.fromCharCode(64 + row)}${col.toString().padStart(2, '0')}`;
}

// 显示座位信息
function showSeatInfo(cell) {
    const seatCode = cell.querySelector('.cell-label').textContent;
    const employeeName = cell.querySelector('.cell-info').textContent;
    
    if (employeeName && employeeName !== '') {
        alert(`座位 ${seatCode}\n员工：${employeeName}`);
    } else {
        alert(`座位 ${seatCode}\n状态：空闲`);
    }
}

// 显示会议室信息
function showMeetingRoomInfo(cell) {
    const roomName = cell.querySelector('.cell-label').textContent;
    const roomInfo = cell.querySelector('.cell-info').textContent;
    alert(`会议室：${roomName}\n${roomInfo}`);
}

// 显示右键菜单
function showCellContextMenu(cell, x, y) {
    // TODO: 实现右键菜单
    console.log('右键菜单', cell, x, y);
}

// 加载布局数据
function loadLayoutData(gridId) {
    // 加载座位信息
    loadSeatAssignments(gridId);
    
    // 加载会议室信息
    loadMeetingRooms(gridId);
}

// 加载会议室信息
function loadMeetingRooms(gridId) {
    fetch(`/api/meeting-rooms/${gridId}`)
        .then(response => response.json())
        .then(rooms => {
            rooms.forEach(room => {
                // 标记会议室区域
                for (let row = room.start_row; row <= room.end_row; row++) {
                    for (let col = room.start_col; col <= room.end_col; col++) {
                        const cell = document.querySelector(`[data-grid-id="${gridId}"][data-row="${row}"][data-col="${col}"]`);
                        if (cell) {
                            cell.className = 'layout-cell meeting-room';
                            cell.dataset.roomId = room.id;
                            
                            // 只在第一个单元格显示会议室名称
                            if (row === room.start_row && col === room.start_col) {
                                cell.querySelector('.cell-label').textContent = room.name;
                                cell.querySelector('.cell-info').textContent = `${room.capacity}人`;
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('加载会议室信息失败:', error);
        });
}

// 切换网格显示
function toggleGrid() {
    const layouts = document.querySelectorAll('.layout-grid');
    layouts.forEach(grid => {
        grid.classList.toggle('show-grid');
    });
}

// 清空布局
function clearLayout() {
    if (confirm('确定要清空当前布局吗？此操作不可恢复。')) {
        const layouts = document.querySelectorAll('.layout-cell');
        layouts.forEach(cell => {
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
        });
    }
}

// 保存布局
function saveLayout() {
    // TODO: 实现保存布局功能
    showAlert('保存功能开发中...', 'info');
}

// 编辑布局
function editLayout(gridId) {
    // TODO: 实现编辑布局功能
    showAlert('编辑布局功能开发中...', 'info');
}

// 全局鼠标事件
document.addEventListener('mousedown', function(e) {
    const isAdmin = document.body.dataset.userIsAdmin === 'true';
    if (e.target.classList.contains('layout-cell') && isAdmin) {
        isDrawing = true;
    }
});

document.addEventListener('mouseup', function(e) {
    isDrawing = false;
    if (isSelecting) {
        endAreaSelection();
    }
});

// 会议室表单提交
document.addEventListener('DOMContentLoaded', function() {
    const meetingRoomForm = document.getElementById('meetingRoomForm');
    if (meetingRoomForm) {
        meetingRoomForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!window.selectedArea) {
                showAlert('请先选择会议室区域', 'warning');
                return;
            }
            
            const formData = new FormData(this);
            const roomData = {
                seat_grid_id: parseInt(window.selectedArea.grid_id),
                name: formData.get('name'),
                capacity: parseInt(formData.get('capacity')),
                start_row: window.selectedArea.start_row,
                start_col: window.selectedArea.start_col,
                end_row: window.selectedArea.end_row,
                end_col: window.selectedArea.end_col,
                room_type: formData.get('room_type'),
                equipment: formData.get('equipment')
            };
            
            createMeetingRoom(roomData)
                .then(result => {
                    if (result.success) {
                        showAlert(result.message, 'success');
                        
                        // 应用会议室样式
                        selectedCells.forEach(cell => {
                            cell.classList.remove('selecting');
                            cell.className = 'layout-cell meeting-room';
                            cell.dataset.roomId = result.room_id;
                        });
                        
                        // 在第一个单元格显示会议室信息
                        if (selectedCells.length > 0) {
                            const firstCell = selectedCells[0];
                            firstCell.querySelector('.cell-label').textContent = roomData.name;
                            firstCell.querySelector('.cell-info').textContent = `${roomData.capacity}人`;
                        }
                        
                        // 清理选择状态
                        selectedCells = [];
                        window.selectedArea = null;
                        
                        // 关闭模态框
                        bootstrap.Modal.getInstance(document.getElementById('meetingRoomModal')).hide();
                    } else {
                        showAlert(result.message || '创建失败', 'danger');
                    }
                });
        });
    }
});

// 创建会议室API调用
function createMeetingRoom(roomData) {
    return fetch('/api/meeting-room', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(roomData)
    })
    .then(response => response.json())
    .catch(error => {
        console.error('创建会议室失败:', error);
        return { success: false, message: '创建失败，请稍后重试' };
    });
}
