// 办公布局管理JavaScript

// 全局变量
let currentLayout = null;
let selectedCells = [];
let isSelecting = false;
let startCell = null;

// 生成办公布局网格
function generateOfficeLayout(container, gridId, rows, cols) {
    container.innerHTML = '';
    container.className = 'office-layout';
    
    // 创建网格容器
    const grid = document.createElement('div');
    grid.className = 'layout-grid';
    grid.style.display = 'grid';
    grid.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
    grid.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
    grid.style.gap = '2px';
    grid.style.padding = '10px';
    grid.style.backgroundColor = '#f8f9fa';
    grid.style.border = '2px solid #dee2e6';
    grid.style.borderRadius = '8px';
    
    // 创建网格单元格
    for (let row = 1; row <= rows; row++) {
        for (let col = 1; col <= cols; col++) {
            const cell = document.createElement('div');
            cell.className = 'layout-cell empty';
            cell.dataset.row = row;
            cell.dataset.col = col;
            cell.dataset.gridId = gridId;
            
            // 添加单元格内容
            cell.innerHTML = `
                <div class="cell-content">
                    <div class="cell-label"></div>
                    <div class="cell-info"></div>
                </div>
            `;
            
            // 添加事件监听器
            setupCellEventListeners(cell);
            
            grid.appendChild(cell);
        }
    }
    
    container.appendChild(grid);
    
    // 加载现有的布局数据
    loadLayoutData(gridId);
}

// 设置单元格事件监听器
function setupCellEventListeners(cell) {
    // 鼠标按下开始选择
    cell.addEventListener('mousedown', function(e) {
        e.preventDefault();
        // 检查用户权限（从全局变量或页面获取）
        const isAdmin = document.body.dataset.userIsAdmin === 'true';
        if (!isAdmin) {
            handleCellClick(this);
            return;
        }

        if (window.drawMode === 'meeting_room') {
            startAreaSelection(this);
        } else {
            handleCellDraw(this);
        }
    });
    
    // 鼠标进入继续选择
    cell.addEventListener('mouseenter', function(e) {
        if (isSelecting && window.drawMode === 'meeting_room') {
            updateAreaSelection(this);
        } else if (isDrawing && window.drawMode !== 'meeting_room') {
            handleCellDraw(this);
        }
    });
    
    // 鼠标抬起结束选择
    cell.addEventListener('mouseup', function(e) {
        if (isSelecting && window.drawMode === 'meeting_room') {
            endAreaSelection();
        }
    });
    
    // 右键菜单
    cell.addEventListener('contextmenu', function(e) {
        e.preventDefault();
        const isAdmin = document.body.dataset.userIsAdmin === 'true';
        if (isAdmin) {
            showCellContextMenu(this, e.clientX, e.clientY);
        }
    });
}

// 处理单元格点击（普通用户）
function handleCellClick(cell) {
    const cellType = cell.classList.contains('seat-occupied') || cell.classList.contains('seat-available') ? 'seat' : 
                    cell.classList.contains('meeting-room') ? 'meeting_room' : 'empty';
    
    if (cellType === 'seat') {
        showSeatInfo(cell);
    } else if (cellType === 'meeting_room') {
        showMeetingRoomInfo(cell);
    }
}

// 处理单元格绘制（管理员）
function handleCellDraw(cell) {
    const mode = window.drawMode;

    switch (mode) {
        case 'seat':
            if (!cell.classList.contains('meeting-room')) {
                // 直接在界面上创建座位，然后调用API保存
                const seatCode = generateSeatCode(cell);
                cell.className = 'layout-cell seat-available';
                cell.querySelector('.cell-label').textContent = seatCode;
                cell.querySelector('.cell-info').textContent = '';

                // 调用API保存到后端
                const gridId = cell.dataset.gridId;
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);

                if (gridId && row && col) {
                    saveSeatToBackend(cell, gridId, row, col, seatCode);
                }
            }
            break;

        case 'obstacle':
            cell.className = 'layout-cell obstacle';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '障碍物';
            break;

        case 'erase':
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
            break;
    }
}

// 保存座位到后端
function saveSeatToBackend(cell, gridId, row, col, seatCode) {
    fetch('/api/seat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            seat_grid_id: parseInt(gridId),
            row_number: row,
            col_number: col,
            seat_code: seatCode
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        if (result.success) {
            cell.dataset.seatId = result.seat_id;
            console.log(`座位 ${seatCode} 保存成功，ID: ${result.seat_id}`);
        } else {
            console.error('保存座位失败:', result);
            // 如果保存失败，恢复界面状态
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
            showAlert(result.error || '保存座位失败', 'danger');
        }
    })
    .catch(error => {
        console.error('保存座位网络错误:', error);
        // 如果保存失败，恢复界面状态
        cell.className = 'layout-cell empty';
        cell.querySelector('.cell-label').textContent = '';
        cell.querySelector('.cell-info').textContent = '';
        showAlert('保存座位失败，请稍后重试', 'danger');
    });
}

// 开始区域选择（会议室）
function startAreaSelection(cell) {
    isSelecting = true;
    startCell = cell;
    selectedCells = [cell];
    cell.classList.add('selecting');
}

// 更新区域选择
function updateAreaSelection(cell) {
    if (!isSelecting || !startCell) return;
    
    // 清除之前的选择
    selectedCells.forEach(c => c.classList.remove('selecting'));
    selectedCells = [];
    
    // 计算选择区域
    const startRow = parseInt(startCell.dataset.row);
    const startCol = parseInt(startCell.dataset.col);
    const endRow = parseInt(cell.dataset.row);
    const endCol = parseInt(cell.dataset.col);
    
    const minRow = Math.min(startRow, endRow);
    const maxRow = Math.max(startRow, endRow);
    const minCol = Math.min(startCol, endCol);
    const maxCol = Math.max(startCol, endCol);
    
    // 选择矩形区域内的所有单元格
    const gridId = cell.dataset.gridId;
    for (let row = minRow; row <= maxRow; row++) {
        for (let col = minCol; col <= maxCol; col++) {
            const targetCell = document.querySelector(`[data-grid-id="${gridId}"][data-row="${row}"][data-col="${col}"]`);
            if (targetCell && !targetCell.classList.contains('meeting-room')) {
                targetCell.classList.add('selecting');
                selectedCells.push(targetCell);
            }
        }
    }
}

// 结束区域选择
function endAreaSelection() {
    isSelecting = false;
    
    if (selectedCells.length > 1) {
        // 显示会议室设置模态框
        showMeetingRoomModal();
    } else {
        // 清除选择
        selectedCells.forEach(cell => cell.classList.remove('selecting'));
        selectedCells = [];
    }
    
    startCell = null;
}

// 显示会议室设置模态框
function showMeetingRoomModal() {
    if (selectedCells.length === 0) return;
    
    const firstCell = selectedCells[0];
    const lastCell = selectedCells[selectedCells.length - 1];
    
    const startRow = Math.min(...selectedCells.map(c => parseInt(c.dataset.row)));
    const endRow = Math.max(...selectedCells.map(c => parseInt(c.dataset.row)));
    const startCol = Math.min(...selectedCells.map(c => parseInt(c.dataset.col)));
    const endCol = Math.max(...selectedCells.map(c => parseInt(c.dataset.col)));
    
    // 更新坐标显示
    document.getElementById('coordinatesText').textContent = 
        `行 ${startRow}-${endRow}, 列 ${startCol}-${endCol} (${selectedCells.length} 个单元格)`;
    
    // 存储选择区域信息
    window.selectedArea = {
        start_row: startRow,
        start_col: startCol,
        end_row: endRow,
        end_col: endCol,
        grid_id: firstCell.dataset.gridId
    };
    
    // 显示模态框
    new bootstrap.Modal(document.getElementById('meetingRoomModal')).show();
}

// 生成座位编号
function generateSeatCode(cell) {
    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);
    return `${String.fromCharCode(64 + row)}${col.toString().padStart(2, '0')}`;
}

// 显示座位信息
function showSeatInfo(cell) {
    const seatCode = cell.querySelector('.cell-label').textContent;
    const employeeName = cell.querySelector('.cell-info').textContent;
    
    if (employeeName && employeeName !== '') {
        alert(`座位 ${seatCode}\n员工：${employeeName}`);
    } else {
        alert(`座位 ${seatCode}\n状态：空闲`);
    }
}

// 显示会议室信息
function showMeetingRoomInfo(cell) {
    const roomName = cell.querySelector('.cell-label').textContent;
    const roomInfo = cell.querySelector('.cell-info').textContent;
    alert(`会议室：${roomName}\n${roomInfo}`);
}

// 显示右键菜单
function showCellContextMenu(cell, x, y) {
    // 移除现有的右键菜单
    const existingMenu = document.querySelector('.context-menu');
    if (existingMenu) {
        existingMenu.remove();
    }

    // 创建右键菜单
    const menu = document.createElement('div');
    menu.className = 'context-menu';
    menu.style.position = 'fixed';
    menu.style.left = x + 'px';
    menu.style.top = y + 'px';
    menu.style.zIndex = '9999';

    const cellType = getCellType(cell);
    let menuItems = [];

    if (cellType === 'seat') {
        menuItems = [
            { text: '分配员工', action: () => assignEmployeeToSeat(cell) },
            { text: '取消分配', action: () => unassignSeat(cell) },
            { text: '编辑座位', action: () => editSeat(cell) },
            { text: '删除座位', action: () => deleteSeat(cell) }
        ];
    } else if (cellType === 'meeting_room') {
        menuItems = [
            { text: '编辑会议室', action: () => editMeetingRoom(cell) },
            { text: '删除会议室', action: () => deleteMeetingRoom(cell) },
            { text: '查看详情', action: () => showMeetingRoomDetails(cell) }
        ];
    } else if (cellType === 'obstacle') {
        menuItems = [
            { text: '编辑障碍物', action: () => editObstacle(cell) },
            { text: '删除障碍物', action: () => deleteObstacle(cell) }
        ];
    } else {
        menuItems = [
            { text: '添加座位', action: () => addSeat(cell) },
            { text: '添加障碍物', action: () => addObstacle(cell) }
        ];
    }

    menuItems.forEach(item => {
        const menuItem = document.createElement('div');
        menuItem.className = 'context-menu-item';
        menuItem.textContent = item.text;
        menuItem.addEventListener('click', () => {
            item.action();
            menu.remove();
        });
        menu.appendChild(menuItem);
    });

    document.body.appendChild(menu);

    // 点击其他地方关闭菜单
    setTimeout(() => {
        document.addEventListener('click', function closeMenu() {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        });
    }, 100);
}

// 获取单元格类型
function getCellType(cell) {
    if (cell.classList.contains('seat-available') || cell.classList.contains('seat-occupied')) {
        return 'seat';
    } else if (cell.classList.contains('meeting-room')) {
        return 'meeting_room';
    } else if (cell.classList.contains('obstacle')) {
        return 'obstacle';
    } else {
        return 'empty';
    }
}

// 右键菜单操作函数
function assignEmployeeToSeat(cell) {
    const seatId = cell.dataset.seatId;
    if (seatId) {
        window.currentSeatId = seatId;
        window.currentSeatCell = cell; // 保存当前单元格引用
        const seatCode = cell.querySelector('.cell-label').textContent;
        const row = cell.dataset.row;
        const col = cell.dataset.col;

        document.getElementById('seatInfo').innerHTML = `
            <strong>座位：</strong>${seatCode} (第${row}排第${col}个位置)
        `;

        loadEmployeeList();
        new bootstrap.Modal(document.getElementById('seatAssignModal')).show();
    } else {
        showAlert('该位置还不是座位，请先添加座位', 'warning');
    }
}

function unassignSeat(cell) {
    const seatId = cell.dataset.seatId;
    if (seatId && confirm('确定要取消该座位的分配吗？')) {
        fetch(`/api/unassign-seat/${seatId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                cell.classList.remove('seat-occupied');
                cell.classList.add('seat-available');
                cell.querySelector('.cell-info').textContent = '';
                showAlert(result.message, 'success');
            } else {
                showAlert(result.message || '取消分配失败', 'danger');
            }
        })
        .catch(error => {
            console.error('取消分配失败:', error);
            showAlert('取消分配失败，请稍后重试', 'danger');
        });
    }
}

function editSeat(cell) {
    const seatCode = cell.querySelector('.cell-label').textContent;
    const newCode = prompt('请输入新的座位编号:', seatCode);
    if (newCode && newCode !== seatCode) {
        cell.querySelector('.cell-label').textContent = newCode;
        showAlert('座位编号已更新', 'success');
    }
}

function deleteSeat(cell) {
    const seatId = cell.dataset.seatId;

    if (confirm('确定要删除这个座位吗？如果有员工分配将一并取消。')) {
        if (seatId) {
            // 调用后端API删除座位
            fetch(`/api/seat/${seatId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(result => {
                if (result.success) {
                    cell.className = 'layout-cell empty';
                    cell.querySelector('.cell-label').textContent = '';
                    cell.querySelector('.cell-info').textContent = '';
                    delete cell.dataset.seatId;
                    showAlert(result.message, 'success');
                } else {
                    showAlert(result.message || '删除失败', 'danger');
                }
            })
            .catch(error => {
                console.error('删除座位失败:', error);
                showAlert('删除失败，请稍后重试', 'danger');
            });
        } else {
            // 如果没有seatId，直接清除界面
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
            showAlert('座位已删除', 'success');
        }
    }
}

function editMeetingRoom(cell) {
    const roomId = cell.dataset.roomId;
    if (roomId) {
        // 获取会议室信息并显示编辑模态框
        showEditMeetingRoomModal(roomId, cell);
    }
}

function deleteMeetingRoom(cell) {
    const roomId = cell.dataset.roomId;
    if (roomId && confirm('确定要删除这个会议室吗？')) {
        deleteMeetingRoomAPI(roomId)
            .then(result => {
                if (result.success) {
                    // 清除会议室区域的所有单元格
                    const gridId = cell.dataset.gridId;
                    const allCells = document.querySelectorAll(`[data-grid-id="${gridId}"][data-room-id="${roomId}"]`);
                    allCells.forEach(c => {
                        c.className = 'layout-cell empty';
                        c.querySelector('.cell-label').textContent = '';
                        c.querySelector('.cell-info').textContent = '';
                        delete c.dataset.roomId;
                    });
                    showAlert('会议室已删除', 'success');
                } else {
                    showAlert(result.message || '删除失败', 'danger');
                }
            });
    }
}

function showMeetingRoomDetails(cell) {
    const roomName = cell.querySelector('.cell-label').textContent;
    const roomInfo = cell.querySelector('.cell-info').textContent;
    const roomId = cell.dataset.roomId;

    // TODO: 获取详细信息并显示
    alert(`会议室详情\n名称：${roomName}\n容量：${roomInfo}\nID：${roomId}`);
}

function addSeat(cell) {
    const gridId = cell.dataset.gridId;
    const row = parseInt(cell.dataset.row);
    const col = parseInt(cell.dataset.col);
    const seatCode = generateSeatCode(cell);

    // 验证参数
    if (!gridId || !row || !col || !seatCode) {
        console.error('添加座位参数错误:', { gridId, row, col, seatCode });
        showAlert('参数错误，无法添加座位', 'danger');
        return;
    }

    // 检查是否已经是座位
    if (cell.classList.contains('seat-available') || cell.classList.contains('seat-occupied')) {
        showAlert('该位置已经是座位了', 'warning');
        return;
    }

    // 检查是否是会议室
    if (cell.classList.contains('meeting-room')) {
        showAlert('不能在会议室位置添加座位', 'warning');
        return;
    }

    console.log('准备添加座位:', {
        seat_grid_id: parseInt(gridId),
        row_number: row,
        col_number: col,
        seat_code: seatCode
    });

    // 先更新界面，给用户即时反馈
    cell.className = 'layout-cell seat-available';
    cell.querySelector('.cell-label').textContent = seatCode;
    cell.querySelector('.cell-info').textContent = '';

    // 调用后端API创建座位
    fetch('/api/seat', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            seat_grid_id: parseInt(gridId),
            row_number: row,
            col_number: col,
            seat_code: seatCode
        })
    })
    .then(response => {
        console.log('API响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(result => {
        console.log('API响应结果:', result);
        if (result.success) {
            cell.dataset.seatId = result.seat_id;
            showAlert(result.message, 'success');
        } else {
            console.error('添加座位失败:', result);
            // 恢复界面状态
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
            showAlert(result.message || result.error || '添加失败', 'danger');
        }
    })
    .catch(error => {
        console.error('添加座位网络错误:', error);
        // 恢复界面状态
        cell.className = 'layout-cell empty';
        cell.querySelector('.cell-label').textContent = '';
        cell.querySelector('.cell-info').textContent = '';
        showAlert(`添加失败: ${error.message}`, 'danger');
    });
}

function addObstacle(cell) {
    cell.className = 'layout-cell obstacle';
    cell.querySelector('.cell-label').textContent = '';
    cell.querySelector('.cell-info').textContent = '障碍物';
    showAlert('障碍物已添加', 'success');
}

function editObstacle(cell) {
    const currentText = cell.querySelector('.cell-info').textContent;
    const newText = prompt('请输入障碍物描述:', currentText);
    if (newText !== null) {
        cell.querySelector('.cell-info').textContent = newText || '障碍物';
        showAlert('障碍物信息已更新', 'success');
    }
}

function deleteObstacle(cell) {
    if (confirm('确定要删除这个障碍物吗？')) {
        cell.className = 'layout-cell empty';
        cell.querySelector('.cell-label').textContent = '';
        cell.querySelector('.cell-info').textContent = '';
        showAlert('障碍物已删除', 'success');
    }
}

// 加载布局数据
function loadLayoutData(gridId) {
    // 加载座位信息
    loadSeatAssignments(gridId);

    // 加载会议室信息
    loadMeetingRooms(gridId);
}

// 加载会议室信息
function loadMeetingRooms(gridId) {
    fetch(`/api/meeting-rooms/${gridId}`)
        .then(response => response.json())
        .then(rooms => {
            rooms.forEach(room => {
                // 标记会议室区域
                for (let row = room.start_row; row <= room.end_row; row++) {
                    for (let col = room.start_col; col <= room.end_col; col++) {
                        const cell = document.querySelector(`[data-grid-id="${gridId}"][data-row="${row}"][data-col="${col}"]`);
                        if (cell) {
                            cell.className = 'layout-cell meeting-room';
                            cell.dataset.roomId = room.id;
                            
                            // 只在第一个单元格显示会议室名称
                            if (row === room.start_row && col === room.start_col) {
                                cell.querySelector('.cell-label').textContent = room.name;
                                cell.querySelector('.cell-info').textContent = `${room.capacity}人`;
                            }
                        }
                    }
                }
            });
        })
        .catch(error => {
            console.error('加载会议室信息失败:', error);
        });
}

// 切换网格显示
function toggleGrid() {
    const layouts = document.querySelectorAll('.layout-grid');
    layouts.forEach(grid => {
        grid.classList.toggle('show-grid');
    });
}

// 清空布局
function clearLayout() {
    if (confirm('确定要清空当前布局吗？此操作不可恢复。')) {
        const layouts = document.querySelectorAll('.layout-cell');
        layouts.forEach(cell => {
            cell.className = 'layout-cell empty';
            cell.querySelector('.cell-label').textContent = '';
            cell.querySelector('.cell-info').textContent = '';
        });
    }
}

// 保存布局
function saveLayout() {
    const layouts = document.querySelectorAll('.office-layout');
    if (layouts.length === 0) {
        showAlert('没有可保存的布局', 'warning');
        return;
    }

    const layoutData = [];

    layouts.forEach(layout => {
        const gridId = layout.dataset.gridId;
        const cells = layout.querySelectorAll('.layout-cell');

        cells.forEach(cell => {
            const cellType = getCellType(cell);
            if (cellType !== 'empty') {
                const cellData = {
                    grid_id: parseInt(gridId),
                    row: parseInt(cell.dataset.row),
                    col: parseInt(cell.dataset.col),
                    type: cellType,
                    label: cell.querySelector('.cell-label').textContent,
                    info: cell.querySelector('.cell-info').textContent,
                    seat_id: cell.dataset.seatId || null,
                    room_id: cell.dataset.roomId || null
                };
                layoutData.push(cellData);
            }
        });
    });

    // 发送保存请求
    fetch('/api/save-layout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ layout_data: layoutData })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('布局保存成功', 'success');
        } else {
            showAlert(result.message || '保存失败', 'danger');
        }
    })
    .catch(error => {
        console.error('保存布局失败:', error);
        showAlert('保存失败，请稍后重试', 'danger');
    });
}

// 编辑布局
function editLayout(gridId) {
    const layout = document.querySelector(`[data-grid-id="${gridId}"]`);
    if (!layout) {
        showAlert('找不到指定的布局', 'danger');
        return;
    }

    // 切换到编辑模式
    toggleEditMode(gridId, true);
    showAlert('已进入编辑模式，您可以修改布局', 'info');
}

// 切换编辑模式
function toggleEditMode(gridId, isEdit) {
    const layout = document.querySelector(`[data-grid-id="${gridId}"]`);
    const card = layout.closest('.card');

    if (isEdit) {
        layout.classList.add('edit-mode');
        card.classList.add('editing');

        // 显示编辑工具栏
        showEditToolbar(card, gridId);
    } else {
        layout.classList.remove('edit-mode');
        card.classList.remove('editing');

        // 隐藏编辑工具栏
        hideEditToolbar(card);
    }
}

// 显示编辑工具栏
function showEditToolbar(card, gridId) {
    const existingToolbar = card.querySelector('.edit-toolbar');
    if (existingToolbar) {
        existingToolbar.remove();
    }

    const toolbar = document.createElement('div');
    toolbar.className = 'edit-toolbar alert alert-info';
    toolbar.innerHTML = `
        <div class="d-flex justify-content-between align-items-center">
            <span><i class="fas fa-edit"></i> 编辑模式：右键单击单元格进行操作</span>
            <div>
                <button class="btn btn-sm btn-success me-2" onclick="saveCurrentLayout(${gridId})">
                    <i class="fas fa-save"></i> 保存
                </button>
                <button class="btn btn-sm btn-secondary" onclick="cancelEdit(${gridId})">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </div>
    `;

    const cardBody = card.querySelector('.card-body');
    cardBody.insertBefore(toolbar, cardBody.firstChild);
}

// 隐藏编辑工具栏
function hideEditToolbar(card) {
    const toolbar = card.querySelector('.edit-toolbar');
    if (toolbar) {
        toolbar.remove();
    }
}

// 保存当前布局
function saveCurrentLayout(gridId) {
    const layout = document.querySelector(`[data-grid-id="${gridId}"]`);
    const cells = layout.querySelectorAll('.layout-cell');

    const layoutData = [];
    cells.forEach(cell => {
        const cellType = getCellType(cell);
        if (cellType !== 'empty') {
            layoutData.push({
                grid_id: parseInt(gridId),
                row: parseInt(cell.dataset.row),
                col: parseInt(cell.dataset.col),
                type: cellType,
                label: cell.querySelector('.cell-label').textContent,
                info: cell.querySelector('.cell-info').textContent
            });
        }
    });

    // 发送保存请求
    fetch(`/api/save-grid-layout/${gridId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ layout_data: layoutData })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('布局保存成功', 'success');
            toggleEditMode(gridId, false);
        } else {
            showAlert(result.message || '保存失败', 'danger');
        }
    })
    .catch(error => {
        console.error('保存布局失败:', error);
        showAlert('保存失败，请稍后重试', 'danger');
    });
}

// 取消编辑
function cancelEdit(gridId) {
    if (confirm('确定要取消编辑吗？未保存的更改将丢失。')) {
        toggleEditMode(gridId, false);
        // 重新加载布局数据
        loadLayoutData(gridId);
        showAlert('已取消编辑', 'info');
    }
}

// 全局鼠标事件
document.addEventListener('mousedown', function(e) {
    const isAdmin = document.body.dataset.userIsAdmin === 'true';
    if (e.target.classList.contains('layout-cell') && isAdmin) {
        isDrawing = true;
    }
});

document.addEventListener('mouseup', function(e) {
    isDrawing = false;
    if (isSelecting) {
        endAreaSelection();
    }
});

// 会议室表单提交
document.addEventListener('DOMContentLoaded', function() {
    const meetingRoomForm = document.getElementById('meetingRoomForm');
    if (meetingRoomForm) {
        meetingRoomForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!window.selectedArea) {
                showAlert('请先选择会议室区域', 'warning');
                return;
            }
            
            const formData = new FormData(this);
            const roomData = {
                seat_grid_id: parseInt(window.selectedArea.grid_id),
                name: formData.get('name'),
                capacity: parseInt(formData.get('capacity')),
                start_row: window.selectedArea.start_row,
                start_col: window.selectedArea.start_col,
                end_row: window.selectedArea.end_row,
                end_col: window.selectedArea.end_col,
                room_type: formData.get('room_type'),
                equipment: formData.get('equipment')
            };
            
            createMeetingRoom(roomData)
                .then(result => {
                    if (result.success) {
                        showAlert(result.message, 'success');
                        
                        // 应用会议室样式
                        selectedCells.forEach(cell => {
                            cell.classList.remove('selecting');
                            cell.className = 'layout-cell meeting-room';
                            cell.dataset.roomId = result.room_id;
                        });
                        
                        // 在第一个单元格显示会议室信息
                        if (selectedCells.length > 0) {
                            const firstCell = selectedCells[0];
                            firstCell.querySelector('.cell-label').textContent = roomData.name;
                            firstCell.querySelector('.cell-info').textContent = `${roomData.capacity}人`;
                        }
                        
                        // 清理选择状态
                        selectedCells = [];
                        window.selectedArea = null;
                        
                        // 关闭模态框
                        bootstrap.Modal.getInstance(document.getElementById('meetingRoomModal')).hide();
                    } else {
                        showAlert(result.message || '创建失败', 'danger');
                    }
                });
        });
    }
});

// 创建会议室API调用
function createMeetingRoom(roomData) {
    return fetch('/api/meeting-room', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(roomData)
    })
    .then(response => response.json())
    .catch(error => {
        console.error('创建会议室失败:', error);
        return { success: false, message: '创建失败，请稍后重试' };
    });
}

// 删除会议室API调用
function deleteMeetingRoomAPI(roomId) {
    return fetch(`/api/meeting-room/${roomId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .catch(error => {
        console.error('删除会议室失败:', error);
        return { success: false, message: '删除失败，请稍后重试' };
    });
}

// 显示编辑会议室模态框
function showEditMeetingRoomModal(roomId, cell) {
    // TODO: 获取会议室详细信息并显示编辑模态框
    const roomName = cell.querySelector('.cell-label').textContent;
    const capacity = cell.querySelector('.cell-info').textContent.replace('人', '');

    // 填充表单
    document.getElementById('roomName').value = roomName;
    document.getElementById('roomCapacity').value = capacity;

    // 更新模态框标题
    document.querySelector('#meetingRoomModal .modal-title').textContent = '编辑会议室';

    // 存储编辑状态
    window.editingRoomId = roomId;
    window.editingCell = cell;

    new bootstrap.Modal(document.getElementById('meetingRoomModal')).show();
}

// 管理员功能 - 编辑布局
function editGrid(gridId) {
    editLayout(gridId);
}

// 管理员功能 - 删除布局
function deleteGrid(gridId) {
    if (confirm('确定要删除这个布局吗？此操作将删除所有座位和会议室，且不可恢复。')) {
        // 首先检查是否有活跃的分配
        checkActiveAssignments(gridId)
            .then(hasAssignments => {
                if (hasAssignments) {
                    if (confirm('该布局中还有员工分配。是否要先取消所有分配然后删除布局？')) {
                        unassignAllSeats(gridId)
                            .then(result => {
                                if (result.success) {
                                    showAlert(`已取消 ${result.count} 个分配，正在删除布局...`, 'info');
                                    setTimeout(() => deleteGridLayout(gridId), 1000);
                                } else {
                                    showAlert(result.message || '取消分配失败', 'danger');
                                }
                            });
                    }
                } else {
                    deleteGridLayout(gridId);
                }
            });
    }
}

// 检查是否有活跃分配
function checkActiveAssignments(gridId) {
    return fetch(`/api/seats/${gridId}`)
        .then(response => response.json())
        .then(seats => {
            return seats.some(seat => seat.employee !== null);
        })
        .catch(error => {
            console.error('检查分配状态失败:', error);
            return false;
        });
}

// 批量取消所有座位分配
function unassignAllSeats(gridId) {
    return fetch(`/api/unassign-all-seats/${gridId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .catch(error => {
        console.error('批量取消分配失败:', error);
        return { success: false, message: '批量取消失败，请稍后重试' };
    });
}

// 删除布局
function deleteGridLayout(gridId) {
    fetch(`/api/seat-grid/${gridId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert(result.message, 'success');
            // 刷新页面
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            showAlert(result.message || '删除失败', 'danger');
        }
    })
    .catch(error => {
        console.error('删除布局失败:', error);
        showAlert('删除失败，请稍后重试', 'danger');
    });
}

// 更新布局中的座位显示（分配成功后）
function updateSeatDisplayInLayout(cell, userId) {
    if (!cell || !userId) return;

    // 获取用户信息
    fetch(`/api/users`)
        .then(response => response.json())
        .then(users => {
            const user = users.find(u => u.id == userId);
            if (user) {
                updateSeatDisplayWithUserInfo(cell, user);
            }
        })
        .catch(error => {
            console.error('获取用户信息失败:', error);
            // 重新加载整个布局数据
            const gridId = cell.dataset.gridId;
            loadLayoutData(gridId);
        });
}

// 使用用户信息直接更新座位显示
function updateSeatDisplayWithUserInfo(cell, user) {
    if (!cell || !user) return;

    // 更新单元格样式和内容
    cell.classList.remove('seat-available');
    cell.classList.add('seat-occupied');
    cell.querySelector('.cell-info').textContent = user.name;

    // 添加工具提示
    cell.title = `${user.name} (${user.employee_id})\n部门：${user.department || '未设置'}\n职位：${user.position || '未设置'}`;

    // 添加成功动画效果
    cell.style.animation = 'pulse-success 0.6s ease-in-out';
    setTimeout(() => {
        cell.style.animation = '';
    }, 600);
}

// 座位分配API调用（复用seating.js中的函数）
function assignSeat(seatId, userId, notes) {
    return fetch('/api/seat-assignment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            seat_id: seatId,
            user_id: userId,
            notes: notes
        })
    })
    .then(response => response.json())
    .catch(error => {
        console.error('分配座位失败:', error);
        return { success: false, message: '分配失败，请稍后重试' };
    });
}

// 批量操作确认对话框
function showBatchOperationDialog(gridId, operation) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">批量操作确认</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要执行以下操作吗？</p>
                    <div class="alert alert-warning">
                        <strong>操作：</strong>${operation}<br>
                        <strong>影响范围：</strong>当前布局的所有相关数据
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirmOperation">
                        <label class="form-check-label" for="confirmOperation">
                            我确认要执行此操作，并了解此操作不可撤销
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmBtn" disabled>确认执行</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    const bootstrapModal = new bootstrap.Modal(modal);
    const confirmBtn = modal.querySelector('#confirmBtn');
    const checkbox = modal.querySelector('#confirmOperation');

    checkbox.addEventListener('change', function() {
        confirmBtn.disabled = !this.checked;
    });

    return new Promise((resolve) => {
        confirmBtn.addEventListener('click', function() {
            bootstrapModal.hide();
            resolve(true);
        });

        modal.addEventListener('hidden.bs.modal', function() {
            modal.remove();
            resolve(false);
        });

        bootstrapModal.show();
    });
}

// 增强的删除布局功能
function deleteGridWithConfirmation(gridId) {
    showBatchOperationDialog(gridId, '删除整个布局（包括所有座位、会议室和分配关系）')
        .then(confirmed => {
            if (confirmed) {
                deleteGrid(gridId);
            }
        });
}

// 批量取消座位分配
function batchUnassignSeats(gridId) {
    showBatchOperationDialog(gridId, '取消该布局中的所有座位分配')
        .then(confirmed => {
            if (confirmed) {
                unassignAllSeats(gridId)
                    .then(result => {
                        if (result.success) {
                            showAlert(`成功取消了 ${result.count} 个座位分配`, 'success');
                            // 更新界面显示
                            updateAllSeatsDisplay(gridId);
                        } else {
                            showAlert(result.message || '批量取消失败', 'danger');
                        }
                    });
            }
        });
}

// 更新所有座位显示状态
function updateAllSeatsDisplay(gridId) {
    const layout = document.querySelector(`[data-grid-id="${gridId}"]`);
    if (layout) {
        const seatCells = layout.querySelectorAll('.layout-cell.seat-occupied');
        seatCells.forEach(cell => {
            cell.classList.remove('seat-occupied');
            cell.classList.add('seat-available');
            cell.querySelector('.cell-info').textContent = '';
            cell.removeAttribute('title');
        });
    }
}

// 显示布局统计信息
function showLayoutStats(gridId) {
    fetch(`/api/seats/${gridId}`)
        .then(response => response.json())
        .then(seats => {
            const totalSeats = seats.length;
            const occupiedSeats = seats.filter(seat => seat.employee !== null).length;
            const availableSeats = totalSeats - occupiedSeats;
            const occupancyRate = totalSeats > 0 ? ((occupiedSeats / totalSeats) * 100).toFixed(1) : 0;

            // 获取会议室信息
            return fetch(`/api/meeting-rooms/${gridId}`)
                .then(response => response.json())
                .then(rooms => {
                    const totalRooms = rooms.length;
                    const totalCapacity = rooms.reduce((sum, room) => sum + room.capacity, 0);

                    showStatsModal({
                        totalSeats,
                        occupiedSeats,
                        availableSeats,
                        occupancyRate,
                        totalRooms,
                        totalCapacity,
                        rooms
                    });
                });
        })
        .catch(error => {
            console.error('获取统计信息失败:', error);
            showAlert('获取统计信息失败', 'danger');
        });
}

// 显示统计信息模态框
function showStatsModal(stats) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">布局统计信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>座位统计</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-4">
                                            <div class="h4 text-primary">${stats.totalSeats}</div>
                                            <small>总座位数</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h4 text-success">${stats.occupiedSeats}</div>
                                            <small>已分配</small>
                                        </div>
                                        <div class="col-4">
                                            <div class="h4 text-warning">${stats.availableSeats}</div>
                                            <small>空闲</small>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="progress">
                                            <div class="progress-bar" style="width: ${stats.occupancyRate}%"></div>
                                        </div>
                                        <small class="text-muted">使用率：${stats.occupancyRate}%</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>会议室统计</h6>
                            <div class="card">
                                <div class="card-body">
                                    <div class="row text-center">
                                        <div class="col-6">
                                            <div class="h4 text-info">${stats.totalRooms}</div>
                                            <small>会议室数量</small>
                                        </div>
                                        <div class="col-6">
                                            <div class="h4 text-info">${stats.totalCapacity}</div>
                                            <small>总容量</small>
                                        </div>
                                    </div>
                                    ${stats.rooms.length > 0 ? `
                                    <div class="mt-3">
                                        <small class="text-muted">会议室列表：</small>
                                        <ul class="list-unstyled mt-2">
                                            ${stats.rooms.map(room => `
                                                <li class="d-flex justify-content-between">
                                                    <span>${room.name}</span>
                                                    <span class="badge bg-secondary">${room.capacity}人</span>
                                                </li>
                                            `).join('')}
                                        </ul>
                                    </div>
                                    ` : '<p class="text-muted mt-3">暂无会议室</p>'}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="exportStatsReport()">导出报告</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);

    modal.addEventListener('hidden.bs.modal', function() {
        modal.remove();
    });

    bootstrapModal.show();
}

// 导出布局
function exportLayout(gridId) {
    showAlert('导出功能开发中...', 'info');
    // TODO: 实现导出功能
}

// 导出统计报告
function exportStatsReport() {
    showAlert('导出报告功能开发中...', 'info');
    // TODO: 实现导出报告功能
}

// 测试添加座位功能
function testAddSeat() {
    const layouts = document.querySelectorAll('.office-layout');
    if (layouts.length === 0) {
        showAlert('没有找到布局，请先创建布局', 'warning');
        return;
    }

    const firstLayout = layouts[0];
    const gridId = firstLayout.dataset.gridId;
    const emptyCells = firstLayout.querySelectorAll('.layout-cell.empty');

    if (emptyCells.length === 0) {
        showAlert('没有找到空白单元格', 'warning');
        return;
    }

    const testCell = emptyCells[0];
    console.log('测试单元格:', testCell);
    console.log('单元格数据:', {
        gridId: testCell.dataset.gridId,
        row: testCell.dataset.row,
        col: testCell.dataset.col
    });

    showAlert('开始测试添加座位...', 'info');
    addSeat(testCell);
}

// 调试信息显示
function showDebugInfo() {
    const layouts = document.querySelectorAll('.office-layout');
    console.log('=== 调试信息 ===');
    console.log('布局数量:', layouts.length);

    layouts.forEach((layout, index) => {
        console.log(`布局 ${index + 1}:`, {
            gridId: layout.dataset.gridId,
            rows: layout.dataset.rows,
            cols: layout.dataset.cols
        });

        const cells = layout.querySelectorAll('.layout-cell');
        console.log(`  单元格数量: ${cells.length}`);

        const emptyCells = layout.querySelectorAll('.layout-cell.empty');
        console.log(`  空白单元格: ${emptyCells.length}`);

        const seatCells = layout.querySelectorAll('.layout-cell.seat-available, .layout-cell.seat-occupied');
        console.log(`  座位单元格: ${seatCells.length}`);
    });
}

// 页面加载完成后显示调试信息
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        showDebugInfo();
    }, 2000);
});
