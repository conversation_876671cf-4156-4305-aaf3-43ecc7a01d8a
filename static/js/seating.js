// 座位表管理JavaScript

// 全局变量
let currentHighlightedSeat = null;

// 工具函数
function showLoading(element) {
    element.innerHTML = '<div class="loading"></div>';
}

function hideLoading(element) {
    element.innerHTML = '';
}

function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 座位表相关函数
function highlightSeat(seatCode) {
    // 清除之前的高亮
    if (currentHighlightedSeat) {
        currentHighlightedSeat.classList.remove('highlighted');
    }
    
    // 查找并高亮新座位
    const seatCell = document.querySelector(`[data-seat-code="${seatCode}"]`);
    if (seatCell) {
        seatCell.classList.add('highlighted');
        currentHighlightedSeat = seatCell;
        
        // 滚动到座位位置
        seatCell.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
        
        return true;
    }
    return false;
}

function clearHighlight() {
    if (currentHighlightedSeat) {
        currentHighlightedSeat.classList.remove('highlighted');
        currentHighlightedSeat = null;
    }
}

// 员工搜索功能
function searchEmployee(employeeId) {
    if (!employeeId.trim()) {
        showAlert('请输入员工工号', 'warning');
        return;
    }

    // 显示加载状态
    const searchResult = document.getElementById('searchResult');
    const employeeInfo = document.getElementById('employeeInfo');

    showLoading(employeeInfo);
    searchResult.style.display = 'block';

    // 调用真实API
    fetch(`/api/search-employee/${employeeId}`)
        .then(response => response.json())
        .then(result => {
            if (result.found) {
                let locationInfo = '';
                if (result.location) {
                    locationInfo = `
                        <hr>
                        <strong>位置信息：</strong><br>
                        ${result.location.city} - ${result.location.building} - ${result.location.floor} - ${result.location.office}<br>
                        座位：${result.location.seat_code}
                    `;
                } else {
                    locationInfo = `
                        <hr>
                        <strong>位置信息：</strong><br>
                        <span class="text-muted">暂未分配座位</span>
                    `;
                }

                employeeInfo.innerHTML = `
                    <strong>员工信息：</strong><br>
                    姓名：${result.employee.name}<br>
                    部门：${result.employee.department || '-'}<br>
                    职位：${result.employee.position || '-'}<br>
                    ${locationInfo}
                `;

                // 设置查看座位按钮
                const goToSeatBtn = document.getElementById('goToSeatBtn');
                if (result.location) {
                    goToSeatBtn.style.display = 'inline-block';
                    goToSeatBtn.onclick = () => {
                        // 如果当前页面就是该员工所在的办公区域，直接高亮座位
                        if (window.location.pathname.includes(`/seating/${result.location.office_id}`)) {
                            if (highlightSeat(result.location.seat_code)) {
                                showAlert('已为您定位到该员工的座位', 'success');
                            }
                        } else {
                            // 跳转到对应的办公区域
                            window.location.href = `/seating/${result.location.office_id}?highlight=${result.location.seat_code}`;
                        }
                    };
                } else {
                    goToSeatBtn.style.display = 'none';
                }
            } else {
                employeeInfo.innerHTML = '<div class="text-danger">未找到该员工信息</div>';
                document.getElementById('goToSeatBtn').style.display = 'none';
            }
        })
        .catch(error => {
            console.error('搜索员工失败:', error);
            employeeInfo.innerHTML = '<div class="text-danger">搜索失败，请稍后重试</div>';
        });
}

// 座位分配功能
function assignSeat(seatId, userId, notes) {
    return fetch('/api/seat-assignment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            seat_id: seatId,
            user_id: userId,
            notes: notes
        })
    })
    .then(response => response.json())
    .catch(error => {
        console.error('分配座位失败:', error);
        return { success: false, message: '分配失败，请稍后重试' };
    });
}

// 创建座位表功能
function createSeatGrid(officeId, name, rows, cols) {
    return fetch('/api/seat-grid', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            office_id: officeId,
            name: name,
            rows: rows,
            cols: cols
        })
    })
    .then(response => response.json())
    .catch(error => {
        console.error('创建座位表失败:', error);
        return { success: false, message: '创建失败，请稍后重试' };
    });
}

// 事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 员工搜索表单
    const searchForm = document.getElementById('searchForm');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const employeeId = document.getElementById('searchEmployeeId').value;
            searchEmployee(employeeId);
        });
    }
    
    // 我的座位按钮
    const mySeatsBtn = document.getElementById('mySeatsBtn');
    if (mySeatsBtn) {
        mySeatsBtn.addEventListener('click', function() {
            // TODO: 实现查看当前用户座位的功能
            showAlert('正在查找您的座位信息...', 'info');
        });
    }
    
    // 创建座位表表单
    const createGridForm = document.getElementById('createGridForm');
    if (createGridForm) {
        createGridForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const name = formData.get('name');
            const rows = parseInt(formData.get('rows'));
            const cols = parseInt(formData.get('cols'));
            
            if (name && rows && cols) {
                createSeatGrid(window.officeId, name, rows, cols)
                    .then(result => {
                        if (result.success) {
                            showAlert(result.message, 'success');
                            // 关闭模态框
                            bootstrap.Modal.getInstance(document.getElementById('createGridModal')).hide();
                            // 刷新页面
                            setTimeout(() => {
                                location.reload();
                            }, 1000);
                        }
                    });
            }
        });
    }
    
    // 座位分配表单
    const seatAssignForm = document.getElementById('seatAssignForm');
    if (seatAssignForm) {
        seatAssignForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const userId = formData.get('user_id');
            const notes = formData.get('notes');
            
            if (userId && window.currentSeatId) {
                assignSeat(window.currentSeatId, userId, notes)
                    .then(result => {
                        if (result.success) {
                            showAlert(result.message, 'success');
                            // 关闭模态框
                            bootstrap.Modal.getInstance(document.getElementById('seatAssignModal')).hide();
                            // 更新座位显示
                            updateSeatDisplay(window.currentSeatId, userId);
                        }
                    });
            }
        });
    }
});

// 更新座位显示
function updateSeatDisplay(seatId, userId) {
    // TODO: 根据用户ID获取用户信息并更新座位显示
    console.log('更新座位显示:', { seatId, userId });
}

// 管理员功能
function editGrid(gridId) {
    // TODO: 实现编辑座位表功能
    showAlert('编辑功能开发中...', 'info');
}

function deleteGrid(gridId) {
    if (confirm('确定要删除这个座位表吗？此操作不可恢复。')) {
        // TODO: 实现删除座位表功能
        showAlert('删除功能开发中...', 'info');
    }
}

// 导出功能（可选）
function exportSeatingChart() {
    // TODO: 实现导出座位表功能
    showAlert('导出功能开发中...', 'info');
}

// 打印功能（可选）
function printSeatingChart() {
    window.print();
}

// 加载座位分配信息
function loadSeatAssignments(gridId) {
    fetch(`/api/seats/${gridId}`)
        .then(response => response.json())
        .then(seats => {
            seats.forEach(seat => {
                const seatCell = document.querySelector(`[data-row="${seat.row}"][data-col="${seat.col}"]`);
                if (seatCell) {
                    seatCell.dataset.seatId = seat.id;

                    if (seat.employee) {
                        seatCell.classList.remove('available');
                        seatCell.classList.add('occupied');
                        seatCell.querySelector('.seat-employee').textContent = seat.employee.name;
                    } else {
                        seatCell.classList.remove('occupied');
                        seatCell.classList.add('available');
                        seatCell.querySelector('.seat-employee').textContent = '';
                    }
                }
            });
        })
        .catch(error => {
            console.error('加载座位信息失败:', error);
        });
}

// 加载员工列表
function loadEmployeeList() {
    fetch('/api/users')
        .then(response => response.json())
        .then(users => {
            const employeeSelect = document.getElementById('employeeSelect');
            employeeSelect.innerHTML = '<option value="">请选择员工</option>';

            users.forEach(user => {
                const option = document.createElement('option');
                option.value = user.id;
                option.textContent = `${user.name} (${user.employee_id})`;
                employeeSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('加载员工列表失败:', error);
        });
}

// 更新座位显示
function updateSeatDisplay(seatId, userId) {
    // 重新加载座位信息
    const seatCell = document.querySelector(`[data-seat-id="${seatId}"]`);
    if (seatCell) {
        const gridId = seatCell.closest('.seat-grid').dataset.gridId;
        loadSeatAssignments(gridId);
    }
}
