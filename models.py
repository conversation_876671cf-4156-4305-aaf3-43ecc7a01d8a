from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(20))
    department = db.Column(db.String(100))
    position = db.Column(db.String(100))
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联座位分配
    seat_assignments = db.relationship('SeatAssignment', foreign_keys='SeatAssignment.user_id', backref='user', lazy=True)

class City(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    buildings = db.relationship('Building', backref='city', lazy=True, cascade='all, delete-orphan')

class Building(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(10), nullable=False)
    address = db.Column(db.String(200))
    city_id = db.Column(db.Integer, db.ForeignKey('city.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    floors = db.relationship('Floor', backref='building', lazy=True, cascade='all, delete-orphan')

class Floor(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)  # 如：1F, 2F, B1
    floor_number = db.Column(db.Integer, nullable=False)
    building_id = db.Column(db.Integer, db.ForeignKey('building.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    offices = db.relationship('Office', backref='floor', lazy=True, cascade='all, delete-orphan')

class Office(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 如：A区, B区, 开发部
    code = db.Column(db.String(10), nullable=False)
    floor_id = db.Column(db.Integer, db.ForeignKey('floor.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    seat_grids = db.relationship('SeatGrid', backref='office', lazy=True, cascade='all, delete-orphan')

class SeatGrid(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    rows = db.Column(db.Integer, nullable=False)  # 行数
    cols = db.Column(db.Integer, nullable=False)  # 列数
    office_id = db.Column(db.Integer, db.ForeignKey('office.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    seats = db.relationship('Seat', backref='seat_grid', lazy=True, cascade='all, delete-orphan')
    meeting_rooms = db.relationship('MeetingRoom', backref='seat_grid', lazy=True, cascade='all, delete-orphan')

class Seat(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    row_number = db.Column(db.Integer, nullable=False)  # 第几排
    col_number = db.Column(db.Integer, nullable=False)  # 第几个位置
    seat_code = db.Column(db.String(20), nullable=False)  # 座位编号，如：A1-01
    is_available = db.Column(db.Boolean, default=True)  # 是否可用
    seat_grid_id = db.Column(db.Integer, db.ForeignKey('seat_grid.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    assignments = db.relationship('SeatAssignment', backref='seat', lazy=True, cascade='all, delete-orphan')

class SeatAssignment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    seat_id = db.Column(db.Integer, db.ForeignKey('seat.id'), nullable=False)
    assigned_at = db.Column(db.DateTime, default=datetime.utcnow)
    assigned_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    notes = db.Column(db.Text)

    # 定义关系
    assigned_by_user = db.relationship('User', foreign_keys=[assigned_by])

    # 确保一个座位同时只能分配给一个人
    __table_args__ = (db.UniqueConstraint('seat_id', 'is_active', name='unique_active_seat'),)

class MeetingRoom(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)  # 会议室名称
    capacity = db.Column(db.Integer, nullable=False)  # 容纳人数
    start_row = db.Column(db.Integer, nullable=False)  # 起始行
    start_col = db.Column(db.Integer, nullable=False)  # 起始列
    end_row = db.Column(db.Integer, nullable=False)    # 结束行
    end_col = db.Column(db.Integer, nullable=False)    # 结束列
    room_type = db.Column(db.String(50), default='meeting')  # 房间类型：meeting, conference, phone_booth等
    equipment = db.Column(db.Text)  # 设备信息（JSON格式）
    seat_grid_id = db.Column(db.Integer, db.ForeignKey('seat_grid.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # 定义关系
    created_by_user = db.relationship('User', foreign_keys=[created_by])

class GridElement(db.Model):
    """网格元素表 - 统一管理座位表中的所有元素"""
    id = db.Column(db.Integer, primary_key=True)
    element_type = db.Column(db.String(20), nullable=False)  # seat, meeting_room, obstacle, empty
    row_number = db.Column(db.Integer, nullable=False)
    col_number = db.Column(db.Integer, nullable=False)
    seat_grid_id = db.Column(db.Integer, db.ForeignKey('seat_grid.id'), nullable=False)

    # 关联的具体对象ID（根据element_type决定）
    seat_id = db.Column(db.Integer, db.ForeignKey('seat.id'), nullable=True)
    meeting_room_id = db.Column(db.Integer, db.ForeignKey('meeting_room.id'), nullable=True)

    # 样式和显示信息
    display_name = db.Column(db.String(100))  # 显示名称
    css_class = db.Column(db.String(100))     # CSS类名

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
