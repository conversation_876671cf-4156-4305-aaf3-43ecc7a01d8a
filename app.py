from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from models import db, User, City, Building, Floor, Office, SeatGrid, Seat, SeatAssignment, MeetingRoom, GridElement
from config import Config

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化扩展
    db.init_app(app)
    
    # 初始化登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'login'
    login_manager.login_message = '请先登录'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        
        # 创建默认管理员用户
        admin = User.query.filter_by(employee_id='admin').first()
        if not admin:
            admin = User(
                employee_id='admin',
                username='admin',
                password_hash=generate_password_hash('admin123'),
                name='系统管理员',
                email='<EMAIL>',
                is_admin=True
            )
            db.session.add(admin)
            db.session.commit()
    
    # 路由定义
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            employee_id = request.form['employee_id']
            password = request.form['password']
            
            user = User.query.filter_by(employee_id=employee_id).first()
            
            if user and check_password_hash(user.password_hash, password):
                login_user(user)
                return redirect(url_for('dashboard'))
            else:
                flash('工号或密码错误')
        
        return render_template('login.html')
    
    @app.route('/logout')
    @login_required
    def logout():
        logout_user()
        return redirect(url_for('login'))
    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        cities = City.query.all()
        return render_template('dashboard.html', cities=cities)
    
    @app.route('/admin')
    @login_required
    def admin():
        if not current_user.is_admin:
            flash('权限不足')
            return redirect(url_for('dashboard'))
        
        cities = City.query.all()
        users = User.query.all()
        return render_template('admin.html', cities=cities, users=users)
    
    @app.route('/api/buildings/<int:city_id>')
    @login_required
    def get_buildings(city_id):
        buildings = Building.query.filter_by(city_id=city_id).all()
        return jsonify([{'id': b.id, 'name': b.name, 'code': b.code} for b in buildings])
    
    @app.route('/api/floors/<int:building_id>')
    @login_required
    def get_floors(building_id):
        floors = Floor.query.filter_by(building_id=building_id).all()
        return jsonify([{'id': f.id, 'name': f.name, 'floor_number': f.floor_number} for f in floors])
    
    @app.route('/api/offices/<int:floor_id>')
    @login_required
    def get_offices(floor_id):
        offices = Office.query.filter_by(floor_id=floor_id).all()
        return jsonify([{'id': o.id, 'name': o.name, 'code': o.code} for o in offices])
    
    @app.route('/seating/<int:office_id>')
    @login_required
    def seating_chart(office_id):
        office = Office.query.get_or_404(office_id)
        seat_grids = SeatGrid.query.filter_by(office_id=office_id).all()
        return render_template('seating_chart.html', office=office, seat_grids=seat_grids)

    @app.route('/office-layout/<int:office_id>')
    @login_required
    def office_layout(office_id):
        office = Office.query.get_or_404(office_id)
        seat_grids = SeatGrid.query.filter_by(office_id=office_id).all()
        return render_template('office_layout.html', office=office, seat_grids=seat_grids)

    # API路由 - 创建座位表
    @app.route('/api/seat-grid', methods=['POST'])
    @login_required
    def create_seat_grid():
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        data = request.get_json()
        office_id = data.get('office_id')
        name = data.get('name')
        rows = data.get('rows')
        cols = data.get('cols')

        if not all([office_id, name, rows, cols]):
            return jsonify({'error': '参数不完整'}), 400

        # 检查办公区域是否存在
        office = Office.query.get(office_id)
        if not office:
            return jsonify({'error': '办公区域不存在'}), 404

        # 创建座位表
        seat_grid = SeatGrid(
            name=name,
            rows=rows,
            cols=cols,
            office_id=office_id,
            created_by=current_user.id
        )
        db.session.add(seat_grid)
        db.session.flush()  # 获取seat_grid.id

        # 创建座位
        for row in range(1, rows + 1):
            for col in range(1, cols + 1):
                seat_code = f"{chr(64 + row)}{col:02d}"
                seat = Seat(
                    row_number=row,
                    col_number=col,
                    seat_code=seat_code,
                    seat_grid_id=seat_grid.id
                )
                db.session.add(seat)

        db.session.commit()
        return jsonify({'success': True, 'message': '座位表创建成功'})

    # API路由 - 获取座位信息
    @app.route('/api/seats/<int:grid_id>')
    @login_required
    def get_seats(grid_id):
        seats = Seat.query.filter_by(seat_grid_id=grid_id).all()
        seat_data = []

        for seat in seats:
            # 获取当前座位分配
            assignment = SeatAssignment.query.filter_by(
                seat_id=seat.id,
                is_active=True
            ).first()

            seat_info = {
                'id': seat.id,
                'row': seat.row_number,
                'col': seat.col_number,
                'code': seat.seat_code,
                'available': seat.is_available,
                'employee': None
            }

            if assignment:
                seat_info['employee'] = {
                    'id': assignment.user.id,
                    'name': assignment.user.name,
                    'employee_id': assignment.user.employee_id,
                    'department': assignment.user.department
                }

            seat_data.append(seat_info)

        return jsonify(seat_data)

    # API路由 - 分配座位
    @app.route('/api/seat-assignment', methods=['POST'])
    @login_required
    def assign_seat():
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        data = request.get_json()
        seat_id = data.get('seat_id')
        user_id = data.get('user_id')
        notes = data.get('notes', '')

        if not all([seat_id, user_id]):
            return jsonify({'error': '参数不完整'}), 400

        # 检查座位和用户是否存在
        seat = Seat.query.get(seat_id)
        user = User.query.get(user_id)

        if not seat or not user:
            return jsonify({'error': '座位或用户不存在'}), 404

        # 检查座位是否已被占用
        existing_assignment = SeatAssignment.query.filter_by(
            seat_id=seat_id,
            is_active=True
        ).first()

        if existing_assignment:
            return jsonify({'error': '该座位已被占用'}), 400

        # 取消用户之前的座位分配
        old_assignments = SeatAssignment.query.filter_by(
            user_id=user_id,
            is_active=True
        ).all()

        for assignment in old_assignments:
            assignment.is_active = False

        # 创建新的座位分配
        new_assignment = SeatAssignment(
            user_id=user_id,
            seat_id=seat_id,
            assigned_by=current_user.id,
            notes=notes
        )

        db.session.add(new_assignment)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '座位分配成功',
            'user': {
                'id': user.id,
                'name': user.name,
                'employee_id': user.employee_id,
                'department': user.department,
                'position': user.position
            }
        })

    # API路由 - 搜索员工
    @app.route('/api/search-employee/<employee_id>')
    @login_required
    def search_employee(employee_id):
        user = User.query.filter_by(employee_id=employee_id).first()

        if not user:
            return jsonify({'found': False, 'message': '未找到该员工'})

        # 查找员工当前的座位分配
        assignment = SeatAssignment.query.filter_by(
            user_id=user.id,
            is_active=True
        ).first()

        result = {
            'found': True,
            'employee': {
                'id': user.id,
                'employee_id': user.employee_id,
                'name': user.name,
                'department': user.department,
                'position': user.position,
                'email': user.email
            }
        }

        if assignment:
            seat = assignment.seat
            office = seat.seat_grid.office
            floor = office.floor
            building = floor.building
            city = building.city

            result['location'] = {
                'city': city.name,
                'building': building.name,
                'floor': floor.name,
                'office': office.name,
                'seat_code': seat.seat_code,
                'office_id': office.id
            }
        else:
            result['location'] = None

        return jsonify(result)

    # API路由 - 获取用户列表
    @app.route('/api/users')
    @login_required
    def get_users():
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        users = User.query.all()
        user_list = []

        for user in users:
            user_list.append({
                'id': user.id,
                'employee_id': user.employee_id,
                'name': user.name,
                'department': user.department,
                'position': user.position
            })

        return jsonify(user_list)

    # API路由 - 创建会议室
    @app.route('/api/meeting-room', methods=['POST'])
    @login_required
    def create_meeting_room():
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        data = request.get_json()
        seat_grid_id = data.get('seat_grid_id')
        name = data.get('name')
        capacity = data.get('capacity')
        start_row = data.get('start_row')
        start_col = data.get('start_col')
        end_row = data.get('end_row')
        end_col = data.get('end_col')
        room_type = data.get('room_type', 'meeting')
        equipment = data.get('equipment', '')

        if not all([seat_grid_id, name, capacity, start_row, start_col, end_row, end_col]):
            return jsonify({'error': '参数不完整'}), 400

        # 检查座位表是否存在
        seat_grid = SeatGrid.query.get(seat_grid_id)
        if not seat_grid:
            return jsonify({'error': '座位表不存在'}), 404

        # 检查区域是否重叠
        existing_rooms = MeetingRoom.query.filter_by(seat_grid_id=seat_grid_id).all()
        for room in existing_rooms:
            if not (end_row < room.start_row or start_row > room.end_row or
                   end_col < room.start_col or start_col > room.end_col):
                return jsonify({'error': '会议室区域重叠'}), 400

        # 创建会议室
        meeting_room = MeetingRoom(
            name=name,
            capacity=capacity,
            start_row=start_row,
            start_col=start_col,
            end_row=end_row,
            end_col=end_col,
            room_type=room_type,
            equipment=equipment,
            seat_grid_id=seat_grid_id,
            created_by=current_user.id
        )

        db.session.add(meeting_room)
        db.session.commit()

        return jsonify({'success': True, 'message': '会议室创建成功', 'room_id': meeting_room.id})

    # API路由 - 获取会议室信息
    @app.route('/api/meeting-rooms/<int:grid_id>')
    @login_required
    def get_meeting_rooms(grid_id):
        meeting_rooms = MeetingRoom.query.filter_by(seat_grid_id=grid_id).all()
        room_data = []

        for room in meeting_rooms:
            room_info = {
                'id': room.id,
                'name': room.name,
                'capacity': room.capacity,
                'start_row': room.start_row,
                'start_col': room.start_col,
                'end_row': room.end_row,
                'end_col': room.end_col,
                'room_type': room.room_type,
                'equipment': room.equipment
            }
            room_data.append(room_info)

        return jsonify(room_data)

    # API路由 - 删除会议室
    @app.route('/api/meeting-room/<int:room_id>', methods=['DELETE'])
    @login_required
    def delete_meeting_room(room_id):
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        meeting_room = MeetingRoom.query.get(room_id)
        if not meeting_room:
            return jsonify({'error': '会议室不存在'}), 404

        db.session.delete(meeting_room)
        db.session.commit()

        return jsonify({'success': True, 'message': '会议室删除成功'})

    # API路由 - 保存布局
    @app.route('/api/save-layout', methods=['POST'])
    @login_required
    def save_layout():
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        data = request.get_json()
        layout_data = data.get('layout_data', [])

        try:
            # 这里可以实现布局数据的持久化
            # 目前只是简单返回成功
            return jsonify({'success': True, 'message': '布局保存成功'})
        except Exception as e:
            return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

    # API路由 - 保存特定网格布局
    @app.route('/api/save-grid-layout/<int:grid_id>', methods=['POST'])
    @login_required
    def save_grid_layout(grid_id):
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        data = request.get_json()
        layout_data = data.get('layout_data', [])

        # 检查座位表是否存在
        seat_grid = SeatGrid.query.get(grid_id)
        if not seat_grid:
            return jsonify({'error': '座位表不存在'}), 404

        try:
            # 清除现有的GridElement数据
            GridElement.query.filter_by(seat_grid_id=grid_id).delete()

            # 保存新的布局数据
            for item in layout_data:
                element = GridElement(
                    element_type=item['type'],
                    row_number=item['row'],
                    col_number=item['col'],
                    seat_grid_id=grid_id,
                    display_name=item.get('label', ''),
                    css_class=item['type']
                )
                db.session.add(element)

            db.session.commit()
            return jsonify({'success': True, 'message': '布局保存成功'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': f'保存失败: {str(e)}'})

    # API路由 - 取消座位分配
    @app.route('/api/unassign-seat/<int:seat_id>', methods=['POST'])
    @login_required
    def unassign_seat(seat_id):
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        # 查找活跃的座位分配
        assignment = SeatAssignment.query.filter_by(
            seat_id=seat_id,
            is_active=True
        ).first()

        if assignment:
            assignment.is_active = False
            db.session.commit()
            return jsonify({'success': True, 'message': '座位分配已取消'})
        else:
            return jsonify({'success': False, 'message': '未找到座位分配记录'})

    # API路由 - 删除座位表/布局
    @app.route('/api/seat-grid/<int:grid_id>', methods=['DELETE'])
    @login_required
    def delete_seat_grid(grid_id):
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        seat_grid = SeatGrid.query.get(grid_id)
        if not seat_grid:
            return jsonify({'error': '座位表不存在'}), 404

        try:
            # 检查是否有活跃的座位分配
            active_assignments = db.session.query(SeatAssignment).join(Seat).filter(
                Seat.seat_grid_id == grid_id,
                SeatAssignment.is_active == True
            ).count()

            if active_assignments > 0:
                return jsonify({
                    'error': '该布局中还有员工分配，请先取消所有分配后再删除',
                    'active_assignments': active_assignments
                }), 400

            # 删除相关数据（级联删除会自动处理）
            db.session.delete(seat_grid)
            db.session.commit()

            return jsonify({'success': True, 'message': '布局删除成功'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

    # API路由 - 批量取消座位分配
    @app.route('/api/unassign-all-seats/<int:grid_id>', methods=['POST'])
    @login_required
    def unassign_all_seats(grid_id):
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        try:
            # 查找该布局下的所有活跃分配
            assignments = db.session.query(SeatAssignment).join(Seat).filter(
                Seat.seat_grid_id == grid_id,
                SeatAssignment.is_active == True
            ).all()

            count = len(assignments)
            for assignment in assignments:
                assignment.is_active = False

            db.session.commit()
            return jsonify({
                'success': True,
                'message': f'已取消 {count} 个座位分配',
                'count': count
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': f'批量取消失败: {str(e)}'})

    # API路由 - 创建座位（动态添加）
    @app.route('/api/seat', methods=['POST'])
    @login_required
    def create_seat():
        try:
            if not current_user.is_admin:
                return jsonify({'error': '权限不足'}), 403

            data = request.get_json()
            if not data:
                return jsonify({'error': '请求数据为空'}), 400

            seat_grid_id = data.get('seat_grid_id')
            row_number = data.get('row_number')
            col_number = data.get('col_number')
            seat_code = data.get('seat_code')

            print(f"创建座位请求: {data}")  # 调试日志

            if not all([seat_grid_id, row_number, col_number, seat_code]):
                missing = []
                if not seat_grid_id: missing.append('seat_grid_id')
                if not row_number: missing.append('row_number')
                if not col_number: missing.append('col_number')
                if not seat_code: missing.append('seat_code')
                return jsonify({'error': f'参数不完整，缺少: {", ".join(missing)}'}), 400

            # 检查座位表是否存在
            seat_grid = SeatGrid.query.get(seat_grid_id)
            if not seat_grid:
                return jsonify({'error': '座位表不存在'}), 404

            # 检查行列是否在有效范围内
            if row_number < 1 or row_number > seat_grid.rows:
                return jsonify({'error': f'行号超出范围 (1-{seat_grid.rows})'}), 400
            if col_number < 1 or col_number > seat_grid.cols:
                return jsonify({'error': f'列号超出范围 (1-{seat_grid.cols})'}), 400

            # 检查座位是否已存在
            existing_seat = Seat.query.filter_by(
                seat_grid_id=seat_grid_id,
                row_number=row_number,
                col_number=col_number
            ).first()

            if existing_seat:
                return jsonify({'error': '该位置已存在座位'}), 400

            # 创建座位
            seat = Seat(
                row_number=row_number,
                col_number=col_number,
                seat_code=seat_code,
                seat_grid_id=seat_grid_id
            )
            db.session.add(seat)
            db.session.commit()

            print(f"座位创建成功: ID={seat.id}, Code={seat_code}")  # 调试日志

            return jsonify({
                'success': True,
                'message': '座位创建成功',
                'seat_id': seat.id
            })
        except Exception as e:
            db.session.rollback()
            print(f"创建座位异常: {str(e)}")  # 调试日志
            return jsonify({'success': False, 'error': f'创建失败: {str(e)}'}), 500

    # API路由 - 删除座位
    @app.route('/api/seat/<int:seat_id>', methods=['DELETE'])
    @login_required
    def delete_seat(seat_id):
        if not current_user.is_admin:
            return jsonify({'error': '权限不足'}), 403

        seat = Seat.query.get(seat_id)
        if not seat:
            return jsonify({'error': '座位不存在'}), 404

        # 检查是否有活跃分配
        active_assignment = SeatAssignment.query.filter_by(
            seat_id=seat_id,
            is_active=True
        ).first()

        if active_assignment:
            return jsonify({'error': '该座位还有员工分配，请先取消分配'}), 400

        try:
            db.session.delete(seat)
            db.session.commit()
            return jsonify({'success': True, 'message': '座位删除成功'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True)
