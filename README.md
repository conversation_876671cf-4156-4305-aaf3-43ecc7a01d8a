# 座位表管理系统

一个基于Flask的企业座位表管理系统，支持多城市、多建筑、多楼层的层级管理，以及座位的可视化分配和查询。

## 功能特性

### 🏢 层级管理
- **城市管理**：支持多个城市的办公地点
- **建筑管理**：每个城市可以有多个建筑
- **楼层管理**：每个建筑可以有多个楼层
- **办公区域**：每个楼层可以划分多个办公区域

### 💺 座位管理
- **可视化座位表**：直观的网格式座位布局
- **灵活配置**：管理员可以自定义座位表的行列数
- **座位分配**：支持员工座位的分配和调整
- **状态显示**：清晰显示座位的占用状态

### 👥 用户管理
- **权限控制**：区分管理员和普通用户权限
- **员工信息**：完整的员工档案管理
- **快速查询**：通过工号快速定位员工位置

### 🔍 查询功能
- **员工定位**：输入工号即可查找员工所在位置
- **座位高亮**：自动高亮显示查询结果
- **跨区域跳转**：支持跨办公区域的座位查看

## 技术栈

- **后端**：Python Flask + SQLAlchemy
- **前端**：HTML5 + CSS3 + JavaScript + Bootstrap 5
- **数据库**：SQLite（开发环境）
- **认证**：Flask-Login

## 快速开始

### 1. 环境准备

确保您的系统已安装Python 3.7+

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 初始化数据库

```bash
python init_data.py
```

这将创建示例数据，包括：
- 2个城市（北京、上海）
- 每个城市3个建筑
- 每个建筑5个楼层
- 每个楼层3个办公区域
- 5个示例员工
- 1个示例座位表（4行6列）

### 4. 启动应用

```bash
python run.py
```

或者直接运行：

```bash
python app.py
```

### 5. 访问系统

打开浏览器访问：http://localhost:5000

## 默认账号

### 管理员账号
- **工号**：admin
- **密码**：admin123

### 普通用户账号
- **工号**：E001 **密码**：123456 （张三）
- **工号**：E002 **密码**：123456 （李四）
- **工号**：E003 **密码**：123456 （王五）
- **工号**：E004 **密码**：123456 （赵六）
- **工号**：E005 **密码**：123456 （孙七）

## 使用说明

### 管理员功能

1. **位置管理**
   - 添加/编辑/删除城市
   - 管理建筑、楼层、办公区域

2. **座位表管理**
   - 创建新的座位表
   - 设置座位表的行列数
   - 分配员工到具体座位

3. **用户管理**
   - 添加/编辑/删除员工
   - 设置用户权限

### 普通用户功能

1. **查看座位表**
   - 选择城市、建筑、楼层、办公区域
   - 查看座位布局和员工分配

2. **员工查询**
   - 输入员工工号查询位置
   - 查看员工详细信息

## 项目结构

```
seating_chart/
├── app.py                 # Flask主应用
├── models.py             # 数据库模型
├── config.py             # 配置文件
├── init_data.py          # 初始化示例数据
├── run.py                # 启动脚本
├── requirements.txt      # 依赖包
├── static/
│   ├── css/
│   │   └── style.css     # 样式文件
│   └── js/
│       └── seating.js    # 前端交互逻辑
├── templates/
│   ├── base.html         # 基础模板
│   ├── login.html        # 登录页面
│   ├── dashboard.html    # 主控制台
│   ├── seating_chart.html # 座位表页面
│   └── admin.html        # 管理员页面
└── README.md             # 项目说明
```

## API接口

### 座位管理
- `POST /api/seat-grid` - 创建座位表
- `GET /api/seats/<grid_id>` - 获取座位信息
- `POST /api/seat-assignment` - 分配座位

### 数据查询
- `GET /api/buildings/<city_id>` - 获取城市下的建筑
- `GET /api/floors/<building_id>` - 获取建筑下的楼层
- `GET /api/offices/<floor_id>` - 获取楼层下的办公区域
- `GET /api/search-employee/<employee_id>` - 搜索员工
- `GET /api/users` - 获取用户列表（管理员）

## 开发说明

### 数据库模型

- **User**: 用户表
- **City**: 城市表
- **Building**: 建筑表
- **Floor**: 楼层表
- **Office**: 办公区域表
- **SeatGrid**: 座位表格配置
- **Seat**: 具体座位信息
- **SeatAssignment**: 座位分配关系

### 权限设计

- **管理员**：可以管理所有数据，包括创建座位表、分配座位、管理用户等
- **普通用户**：只能查看座位表和查询员工信息

## 扩展功能

系统预留了以下扩展接口：

1. **批量导入**：支持Excel批量导入员工信息
2. **报表导出**：导出座位分配报表
3. **移动端适配**：响应式设计支持移动设备
4. **通知系统**：座位变更通知
5. **历史记录**：座位分配历史追踪

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。
