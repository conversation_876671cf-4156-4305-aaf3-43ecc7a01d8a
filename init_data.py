#!/usr/bin/env python3
"""
初始化示例数据脚本
"""

from app import create_app
from models import db, User, City, Building, Floor, Office, SeatGrid, Seat, MeetingRoom, GridElement
from werkzeug.security import generate_password_hash

def init_sample_data():
    app = create_app()
    
    with app.app_context():
        # 清空现有数据（除了管理员）
        print("清理现有数据...")
        
        # 创建示例城市
        print("创建示例城市...")
        beijing = City(name='北京', code='BJ')
        shanghai = City(name='上海', code='SH')
        db.session.add_all([beijing, shanghai])
        db.session.flush()
        
        # 创建示例建筑
        print("创建示例建筑...")
        bj_building_a = Building(name='A座', code='A', address='北京市朝阳区xxx路1号', city_id=beijing.id)
        bj_building_b = Building(name='B座', code='B', address='北京市朝阳区xxx路2号', city_id=beijing.id)
        sh_building_a = Building(name='A座', code='A', address='上海市浦东新区xxx路1号', city_id=shanghai.id)
        db.session.add_all([bj_building_a, bj_building_b, sh_building_a])
        db.session.flush()
        
        # 创建示例楼层
        print("创建示例楼层...")
        floors = []
        for building in [bj_building_a, bj_building_b, sh_building_a]:
            for i in range(1, 6):  # 1-5层
                floor = Floor(name=f'{i}F', floor_number=i, building_id=building.id)
                floors.append(floor)
        db.session.add_all(floors)
        db.session.flush()
        
        # 创建示例办公区域
        print("创建示例办公区域...")
        offices = []
        office_names = ['开发区A', '开发区B', '产品部', '设计部', '运营部']
        
        for floor in floors:
            for i, office_name in enumerate(office_names[:3]):  # 每层3个办公区域
                office = Office(name=office_name, code=f'O{i+1}', floor_id=floor.id)
                offices.append(office)
        db.session.add_all(offices)
        db.session.flush()
        
        # 创建示例用户
        print("创建示例用户...")
        users = [
            User(
                employee_id='E001',
                username='zhangsan',
                password_hash=generate_password_hash('123456'),
                name='张三',
                email='<EMAIL>',
                department='技术部',
                position='高级工程师'
            ),
            User(
                employee_id='E002',
                username='lisi',
                password_hash=generate_password_hash('123456'),
                name='李四',
                email='<EMAIL>',
                department='技术部',
                position='软件工程师'
            ),
            User(
                employee_id='E003',
                username='wangwu',
                password_hash=generate_password_hash('123456'),
                name='王五',
                email='<EMAIL>',
                department='产品部',
                position='产品经理'
            ),
            User(
                employee_id='E004',
                username='zhaoliu',
                password_hash=generate_password_hash('123456'),
                name='赵六',
                email='<EMAIL>',
                department='设计部',
                position='UI设计师'
            ),
            User(
                employee_id='E005',
                username='sunqi',
                password_hash=generate_password_hash('123456'),
                name='孙七',
                email='<EMAIL>',
                department='运营部',
                position='运营专员'
            )
        ]
        db.session.add_all(users)
        db.session.flush()
        
        # 为第一个办公区域创建示例座位表
        print("创建示例座位表...")
        first_office = offices[0]
        
        # 获取管理员用户
        admin = User.query.filter_by(employee_id='admin').first()
        
        seat_grid = SeatGrid(
            name='开发区A座位表',
            rows=4,
            cols=6,
            office_id=first_office.id,
            created_by=admin.id
        )
        db.session.add(seat_grid)
        db.session.flush()
        
        # 创建座位
        print("创建座位...")
        seats = []
        for row in range(1, 5):  # 4行
            for col in range(1, 7):  # 6列
                seat_code = f"{chr(64 + row)}{col:02d}"
                seat = Seat(
                    row_number=row,
                    col_number=col,
                    seat_code=seat_code,
                    seat_grid_id=seat_grid.id
                )
                seats.append(seat)
        db.session.add_all(seats)
        db.session.flush()
        
        print("提交数据...")
        db.session.commit()
        
        print("示例数据初始化完成！")
        print("\n可用的测试账号：")
        print("管理员: admin / admin123")
        print("普通用户:")
        for user in users:
            print(f"  {user.employee_id} / 123456 ({user.name})")
        
        print(f"\n创建了 {len(offices)} 个办公区域")
        print(f"创建了 {len(seats)} 个座位")

if __name__ == '__main__':
    init_sample_data()
